I'm ready to begin implementing features from the Trodoo venue booking application based on the architecture documentation in `docs/02-new-expanded-arch.md` and the UI/UX specifications in `docs/03-features-design.md`. 

For each feature I request, please:

1. **First, analyze the requirements** by reviewing the relevant documentation files to understand:
   - The feature's technical requirements and architecture
   - UI/UX design specifications and user flows (make adjustments where necessay)
   - Integration points with existing systems (PocketBase, payment services, etc.)
   - Dependencies on other features or components

2. **Create a comprehensive task breakdown** that includes:
   - Granular, achievable tasks (each representing ~20 minutes of development work)
   - Clear task hierarchy with subtasks where appropriate
   - Dependencies between tasks clearly identified
   - Specific deliverables for each task (components, API endpoints, database schemas, etc.)

3. **Follow the established project structure** from our previous work:
   - Use the Trodoo design system (design.json as single source of truth)
   - Maintain the organized file structure (auth, common, core, dashboard, admin, venues, transactions)
   - Integrate with existing PocketBase collections and authentication (use the pocketbase-server mcp to interact my pocketbase instatnce)
   - Follow Astro v5.10 + TypeScript + Tailwind patterns

4. **Use task management tools** to track progress and ensure nothing is missed

5. **Use context7 mcp server** for updated documentation on libraries for up to date implementation

I will provide you with Feature 1 details next, and you should create a detailed implementation plan with trackable tasks before beginning any code changes.