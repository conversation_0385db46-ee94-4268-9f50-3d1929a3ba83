<goal>
You are an industry-veteran SaaS product designer. You've built high-touch Uls for FANG-style
Your goal is to take the context below, the guidelines, and the user inspiration, and turn it into a functional UX/Ul style-guide 
</goal>

<inspirations>
Use content in <style-guide> below for inspiration and add improvement where necessary

</inspirations>

< guidelines>
	 <aesthetics>
-	Bold simplicity with intuitive navigation creating frictionless experiences
-	Breathable whitespace complemented by strategic color accents for visual hierarchy
-	Strategic negative space calibrated for cognitive breathing room and content prioritization
-	Systematic color theory applied through subtle gradients and purposeful accent placement
-	Typography hierarchy utilizing weight variance and proportional scaling for information architecture
-	Visual density optimization balancing information availability with cognitive load management
-	Motion choreography implementing physics-based transitions for spatial continuity Accessibility-driven contrast ratios paired with intuitive navigation patterns ensuring universal usability
-	Feedback responsiveness via state transitions communicating system status with minimal latency
-	Content-first layouts prioritizing user objectives over decorative elements for task efficiency
</aesthetics>

<practicalities>
This will'be a webapp/website
</practicalities> 
</guidelines>

<context>

	<app-overview>
We are building a comprehensive, user-friendly digital platform that directly connects individuals and organizations looking to rent venues or properties with property owners. The platform will facilitate the entire rental lifecycle, from discovery and virtual touring to booking, payment, and post-rental communication, creating a seamless and efficient marketplace.
      </app-overview> 
	<task>
Your goal here is to go feature-by-feature and think like a designer. Here is a list of things you'd absolutely need to think about:
User goals and tasks - Understanding what users need to accomplish and designing to make those primary tasks seamless and efficient
Information architecture - Organizing content and features in a logical hierarchy that matches users' mental models
Progressive disclosure - Revealing complexity gradually to avoid overwhelming users while still providing access to advanced features
Visual hierarchy - Using size, color, contrast, and positioning to guide attention to the most important elements first
Affordances and signifiers - Making interactive elements clearly identifiable through visual cues that indicate how they work
Consistency - Maintaining uniform patterns, components, and interactions across screens to reduce cognitive load
Accessibility - Ensuring the design works for users of all abilities (color contrast, screen readers, keyboard navigation)
Error prevention - Designing to help users avoid mistakes before they happen rather than just handling errors after they occur
Feedback - Providing clear signals when actions succeed or fail, and communicating system status at all times
Performance considerations - Accounting for loading times and designing appropriate loading states
Mobile vs. desktop considerations - Adapting layouts and interactions for different device capabilities and contexts
Responsive design - Ensuring the interface works well across various screen sizes and orientations
User testing feedback loops - Incorporating iterative testing to validate assumptions and improve the design
Platform conventions - Following established patterns from iOS/Android/Web to meet user
expectations
Microcopy and content strategy - Crafting clear, concise text that guides users through the
experience
Aesthetic appeal - Creating a visually pleasing design that aligns with brand identity while prioritizing usability
Animations - Crafting beautiful yet subtle animations and transitions that make the app feel professional

I need you to take EACH FEATURE below, and give me a cohesive Design Brief. Here's how I want it formatted. You repeat this for each feature:
< format>
## Feature Name
#### Screen X
##### Screen X State N
*description
*of
*UI & UX
*in detail
*including animations
*any anything else and colors based on the style-guide below
#### Screen X State N + 1

Repeat as many N+# as needed based on the function of the state
</format>
</task>

<feature-list>


Feature Specifications
Feature 1: User, Admin & Profile Core
Feature Goal: To establish a secure and robust identity system that handles user registration, authentication, role-based access control (RBAC), and content moderation primitives. This is the bedrock of platform trust and safety.

API Relationships: This feature exclusively uses the Pocketbase API for all its operations. The frontend client will interact directly with the Pocketbase instance.

Detailed Feature Requirements:

Users must be able to register with an email and password.

Passwords must meet strength requirements (e.g., min 8 chars, 1 uppercase, 1 number).

Upon registration, users are assigned the 'renter' role by default. They can add the 'owner' role from their profile.

Users must log in to access any authenticated parts of the application.

A user with the 'admin' role has access to a separate dashboard.

The admin dashboard must display a list of all users and a list of all flagged content.

Admins must be able to view user details and manually change a user's role or deactivate their account.

Admins must be able to view flagged content details and resolve flags (marking them as 'resolved').

Detailed Implementation Guide:
(This section remains unchanged from the previous version, as it is foundational and correct.)

Feature 2: Venue Marketplace (Listing, Touring & Search)
Feature Goal: To provide a fluid and intuitive interface for owners to merchandise their venues effectively, including immersive virtual tours, and for renters to discover them through a powerful, fast search experience.

API Relationships: The frontend interacts with Pocketbase for all CRUD operations on venues and with Meilisearch for all search queries.

Detailed Feature Requirements:

An authenticated owner must be able to access a multi-step "Create Venue" form.

The form must capture title, description, address, capacity, pricing, amenities (as selectable tags), standard photos, one equirectangular 360° photo, and an optional PDF rental agreement.

(REQ010) The listing page must feature a native, interactive 360° photo viewer.

The search interface must provide real-time, typo-tolerant results as the user types.

Any authenticated user must be able to click a "Flag" button on a venue page, which opens a modal to submit a reason for flagging.

Detailed Implementation Guide:
(This section remains unchanged from the previous version, as it correctly incorporates the virtual tour viewer and search functionality.)

Feature 3: Transaction Lifecycle (Booking, Messaging, Payments & Payouts)
Feature Goal: To manage the entire commercial flow in a secure, reliable, and auditable manner, from initial renter inquiry and communication, through booking and payment, to final owner payout.

API Relationships: Frontend <-> Pocketbase (Booking & Chat), Frontend -> Paystack (Payment), Deno Service <-> Paystack (Payouts & Webhooks).

Detailed Feature Requirements:

On a venue page, an authenticated renter can select a start and end date/time and request to book.

This request must trigger a check against existing confirmed bookings for that venue to prevent double-booking.

The property owner receives a notification and can approve or deny the request from their dashboard.

Upon approval, the renter receives a notification and a prompt to pay.

The renter pays the full amount via Paystack.

Upon successful payment, the booking status is updated, and both parties are notified.

After the booking's end date has passed, a payout is automatically scheduled for the owner.

(REQ008) A simple, real-time chat must be available within the context of a single booking, accessible only to the renter and owner.

Detailed Implementation Guide:
(This section remains unchanged from the previous version, as it correctly incorporates the real-time messaging window and transaction flow.)

Feature 4: Community Trust & Feedback (Reviews & Ratings)
Feature Goal: (REQ009) To build a trustworthy marketplace by enabling renters to share their experiences and provide a transparent feedback loop for venue quality and owner conduct.

API Relationships: Frontend <-> Pocketbase. The Deno service may be used to calculate and cache average ratings periodically.

Detailed Feature Requirements:

After a booking's status becomes completed, the renter receives a prompt (via email and in-app notification) to leave a review.

The review form must capture a star rating (1-5) for "Overall Experience" and a free-text comment.

Submitted reviews must be associated with the specific booking, user, and venue.

Property owners must be notified of new reviews and have the ability to write a single, public response.

The average star rating and a list of all reviews (with owner responses nested) must be displayed on the public venue listing page.

Detailed Implementation Guide:

System Architecture: This is a client-to-BaaS feature. The frontend handles form submission, and the backend (Pocketbase) stores the data. A cron job in the Deno service can be added to periodically update a cached average_rating field on the venues collection to optimize frontend load times.

Database Schema Design:

reviews Collection:

id: (PK, default)

booking: (Relation to bookings, Required, Unique - one review per booking)

renter: (Relation to users, Required)

venue: (Relation to venues, Required)

rating: (Number, Required, Min: 1, Max: 5)

comment: (Text, Required)

owner_response: (Text, optional)

Comprehensive API Design (Pocketbase):

POST /api/collections/reviews/records: Create a review. API Rule: @request.auth.id = @request.data.renter && @request.data.booking.status = "completed".

GET /api/collections/reviews/records?filter=(venue.id='VENUE_ID'): List reviews for a venue. Publicly accessible.

PATCH /api/collections/reviews/records/:id: Update a review (for owner response). API Rule: @request.auth.id = owner.id && owner_response = "" (Can only respond once).

Frontend Architecture:

ReviewForm.jsx: A modal component that includes the StarRating.jsx component and a textarea.

ReviewList.jsx: A component on the venue page that fetches and displays all reviews, nesting the owner_response where it exists.

Detailed CRUD Operations:

Create: Renter submits the form. The API rule ensures they can only review a booking they've completed.

Read: Reviews are publicly readable on the venue page, sorted by most recent.

Update: An owner can add an owner_response one time. No other updates are allowed.

Delete: Deletions are admin-only operations for moderation purposes.

Security Considerations: The API rules are critical to prevent review spam or fraudulent reviews. All comments must be sanitized on the frontend before display to prevent XSS.

Testing Strategy: Integration test for the API rule that blocks reviews for incomplete bookings. E2E test for leaving a review and then seeing it appear on the venue page.

Data Management: Average ratings should be calculated and cached on the venues collection to avoid N+1 queries on the search page. Review lists will be paginated.

Error Handling & Logging: If a user tries to review a booking they are not authorized for, the API will return a 403, and the frontend will show a generic error message.

Feature 5: Engagement & Logistics (Alerts, Checklists, Invitations)
Feature Goal: (REQ013, REQ014, REQ015) To automate communication and streamline logistical tasks around a booking, providing value beyond the transaction and improving the user experience for both renters and owners.

API Relationships: Frontend <-> Pocketbase (for data). Frontend -> Deno Service (to trigger emails). Deno Service -> Email Provider (e.g., Resend).

Detailed Feature Requirements:

Alerts: Key events (new booking request, confirmation, payment) must trigger automated emails to the relevant parties.

Checklists: Owners can define a simple move-out checklist template for their venues. Before checkout, renters can access and complete this checklist.

Invitations: After a booking is paid, renters have an option to create a simple event invitation, input guest emails, and have the system send them out.

Detailed Implementation Guide:

System Architecture: Event-driven. Actions in Pocketbase (like a booking status change) will trigger webhooks or be picked up by cron jobs in the Deno service, which then uses an email provider to send notifications.

Database Schema Design:

(Update) venues Collection: Add a field move_out_checklist: (JSON, optional, e.g., [{"item": "Take out trash", "checked": false}, ...]).

checklist_submissions Collection:

id, booking: (Relation, Required, Unique), renter: (Relation, Required)

submission_data: (JSON, Required) // Stores the completed checklist

notes: (Text)

Comprehensive API Design:

The Deno Service will need new internal functions/endpoints triggered by cron jobs or other services.

POST /api/internal/send-invitations: An internal endpoint the frontend could call, or this logic could be in a serverless function. It would take a bookingId and a list of emails.

Frontend Architecture:

ChecklistForm.jsx: A component for owners to create the JSON checklist template in their venue settings.

ChecklistSubmission.jsx: A component for renters, displaying the checklist items as a series of checkboxes.

InvitationForm.jsx: A simple form for the renter to compose a message and list guest emails.

Detailed CRUD Operations:

Checklists: The checklist template is Updated on the venues record by the owner. A checklist_submission is Created once by the renter for a booking.

Invitations: This is an action, not a CRUD operation. It triggers an email-sending job.

Security Considerations: The email invitation endpoint must have strict rate limiting to prevent abuse as an email spamming tool. Checklists submitted by renters must have their text content sanitized.

Testing Strategy: Test the cron job for sending reminders. Test the invitation email formatting and delivery. Test that a renter can successfully fill out and submit a checklist.

Data Management: Checklists are stored as JSON. Email templates can be stored as simple text files in the Deno service repository.

Error Handling & Logging: If the email service provider API is down, the Deno service should log the error and retry sending the notification/invitation later. The user should be informed if their batch of invitations could not be sent.

Feature 6: System Services (Expanded)
Feature Goal: To create a reliable, decoupled backend service that handles all asynchronous tasks, including search syncing, payment processing, and the newly added notification/engagement features.

API Relationships: This service acts as the glue code. Deno <-> Pocketbase, Deno <-> Meilisearch, Paystack <-> Deno, Deno <-> Email Provider.

  * **Detailed Feature Requirements:**

      * **Sync Service:** Any create, update, or delete operation on a published venue in Pocketbase must be reflected in the Meilisearch index within seconds.
      * **Webhook Service:** Must reliably receive and process payment confirmation webhooks from Paystack. Must be resilient to duplicate events.
      * **Payout Service:** Must run on a regular schedule (e.g., daily) to process pending payouts for completed bookings.
      * **Notification Service:** Must run on a schedule (e.g., hourly) to query for upcoming events and send email reminders. Must also be able to send transactional emails on demand (e.g., for invitations).

  * **Detailed Implementation Guide:**

    1.  **System Architecture:** A single Deno application deployed in a Docker container on Fly.io/Railway. The application will run a web server (for the webhook) and a background process (for the sync and scheduled payouts) concurrently.  Within the existing Deno application, add a new notificationService.ts. This service will be called by other services (e.g., after a webhook is processed) and by a new cron job.
    2.  **Implementation - Sync Service:**
          * On startup, the Deno service authenticates with Pocketbase as an admin.
          * It subscribes to the `venues` collection: `pb.collection('venues').subscribe('*', ...)`
          * Inside the callback, it receives an `event` object (`event.action`, `event.record`).
          * `if (event.action === 'create' || event.action === 'update')`: format the `event.record` into the structure Meilisearch expects and call `meiliClient.index('venues').addDocuments([record])`.
          * `if (event.action === 'delete')`: call `meiliClient.index('venues').deleteDocument(event.record.id)`.
          * Include a startup script to perform a full one-time sync of all existing venues to handle initial deployment and disaster recovery.
    3.  **Implementation - Webhook Service:**
          * Use a Deno web framework like Oak. Define the `POST /api/webhooks/paystack` route.
          * **Step 1: Verify Signature.** Create a crypto HMAC-SHA512 hash of the request body using your Paystack secret key. Compare it to the `x-paystack-signature` header. If they don't match, return `400 Bad Request` immediately.
          * **Step 2: Check Idempotency.** When a `charge.success` event comes in, check in the `bookings` collection if a booking with the same `paystack_ref` already has the status `paid`. If so, return `200 OK` to acknowledge the webhook but do nothing further.
          * **Step 3: Process.** If the signature is valid and the event is new, find the booking, update its status to `paid`, and save the record.
    4.  **Implementation - Payout Service:**
          * Use a library like `deno-cron` to schedule a function `processPayouts` to run once daily.
          * The function will:
              * Get the current date. Find all `bookings` where `status = 'paid'` and `end_date` is in the past. Update their status to `completed`.
              * Find all `bookings` where `status = 'completed'` and for which no `payout` record exists.
              * For each of these, calculate the `payout_amount` (`total_price` - `platform_fee`).
              * Call the Paystack Transfers API to send the `payout_amount` to the owner's `paystack_recipient_code`.
              * Create a `payouts` record to log the transaction.
    5.  **Implementation - Notification Service:**
        * Email Provider Integration: Use a library or fetch to interact with an email service like Resend. The API key will be stored as a secure environment variable.

        Cron Job for Reminders:

        cron.schedule('0 * * * *', ...) // Run every hour

        The job queries Pocketbase for bookings where status = 'paid' and start_date is within the next 24-25 hours.

        It checks against a notifications log collection to ensure a reminder hasn't already been sent for this booking.

        If not sent, it calls the email service to send a "Your booking is tomorrow!" email and creates a log entry.

        On-Demand Emails (Invitations):

        Create a function sendInvitationEmail(booking, guestEmails, message).

        This function iterates through the emails, personalizes the MJML template, and calls the email provider API for each one.

        Database Schema Design:

        notifications Collection:

        id, booking: (Relation, Required), user: (Relation, Required)

        type: (Select: 'booking_confirmed', 'payment_reminder', 'event_upcoming', etc.)

        sent_at: (Date, Required)

        Error Handling & Logging: All calls to the email provider API must be wrapped in try...catch. Failures should be logged with the specific error from the provider. For critical notifications, a retry mechanism with exponential backoff should be implemented..
    6.  **Security:** This service is the most sensitive part of the architecture. All secret keys (Pocketbase admin, Meilisearch admin, Paystack secret) **MUST** be injected as environment variables into the container and should never be hardcoded. Network policies on the hosting provider should limit ingress traffic to only what's necessary (e.g., port 443 from Paystack's IP range for the webhook).
    7.  **Testing Strategy:** Pure unit tests for the data transformation logic (PB record -\> MS document). Integration tests for each service function using mocked SDK clients (mock `pb`, `meili`, `paystack`).
    8.  **Data Management:** This service is stateless. Its primary job is to read state from one service and update state in another.
    9.  **Error Handling & Logging:** Wrap all external API calls in `try...catch` blocks. If the Meilisearch sync fails, log the failed record ID and retry with exponential backoff. If a payout fails, update the associated `payouts` record with an 'error' status and reason, and send an alert to an admin channel (e.g., a Slack webhook). Use structured JSON logging for all operations.
</feature-list>

<style-guide>

{
  "designSystem": {
    "name": "Modern Venue Rental Platform",
    "version": "1.1.0",
    "description": "A comprehensive design system for a sleek, modern venue rental service. It features a clean layout, a vibrant and professional color palette, and user-friendly components designed to build trust and streamline the booking process."
  },
  "brandPersonality": {
    "tone": "professional, trustworthy, approachable, efficient",
    "visualStyle": "modern, clean, premium, vibrant",
    "userExperience": "effortless, confident, reliable, seamless",
    "targetAudience": "business professionals, organizers, quality-conscious consumers"
  },
  "colorPalette": {
    "primary": {
      "green": "#28A745",
      "greenLight": "#34C759",
      "greenDark": "#218838"
    },
    "secondary": {
      "yellow": "#FFC107",
      "yellowLight": "#FFD03B",
      "yellowDark": "#E0A800"
    },
    "neutral": {
      "white": "#FFFFFF",
      "lightGray": "#F8F9FA",
      "mediumGray": "#E9ECEF",
      "darkGray": "#6C757D",
      "black": "#343A40"
    },
    "background": {
      "gradientPrimary": "linear-gradient(135deg, #28A745 0%, #34C759 100%)",
      "overlayPattern": "Subtle transparent tire track or geometric pattern overlay"
    }
  },
  "typography": {
    "fontFamily": {
      "primary": "'Poppins', 'Helvetica Neue', Arial, sans-serif",
      "body": "'Roboto', 'Helvetica Neue', Arial, sans-serif",
      "fallback": "system-ui, -apple-system, sans-serif"
    },
    "fontWeights": {
      "regular": 400,
      "medium": 500,
      "semibold": 600,
      "bold": 700
    },
    "headings": {
      "h1": {
        "fontSize": "48px",
        "fontWeight": 700,
        "lineHeight": 1.2,
        "color": "neutral.white",
        "usage": "Hero headlines"
      },
      "h2": {
        "fontSize": "32px",
        "fontWeight": 600,
        "lineHeight": 1.3,
        "color": "neutral.black",
        "usage": "Section headers"
      },
      "h3": {
        "fontSize": "20px",
        "fontWeight": 600,
        "lineHeight": 1.4,
        "color": "neutral.black",
        "usage": "Card titles, feature headers"
      }
    },
    "body": {
      "large": {
        "fontSize": "18px",
        "fontWeight": 400,
        "lineHeight": 1.6,
        "color": "neutral.white",
        "usage": "Hero descriptions"
      },
      "regular": {
        "fontSize": "16px",
        "fontWeight": 400,
        "lineHeight": 1.5,
        "usage": "General body text, feature descriptions"
      },
      "small": {
        "fontSize": "14px",
        "fontWeight": 500,
        "lineHeight": 1.4,
        "usage": "Form labels, secondary text"
      }
    }
  },
  "spacing": {
    "scale": "8px base unit",
    "values": {
      "xs": "4px",
      "sm": "8px",
      "md": "16px",
      "lg": "24px",
      "xl": "32px",
      "2xl": "48px",
      "3xl": "64px"
    }
  },
  "borderRadius": {
    "sm": "4px",
    "md": "8px",
    "lg": "12px",
    "xl": "20px",
    "full": "9999px"
  },
  "shadows": {
    "subtle": "0 2px 4px rgba(0, 0, 0, 0.05)",
    "medium": "0 4px 8px rgba(0, 0, 0, 0.1)",
    "large": "0 10px 20px rgba(0, 0, 0, 0.15)",
    "card": "0 8px 16px rgba(0,0,0,0.1)"
  },
  "layout": {
    "containerWidth": "1200px",
    "header": {
      "structure": "Horizontal navigation bar with logo left and links right",
      "background": "neutral.white",
      "height": "80px",
      "shadow": "subtle"
    },
    "hero": {
      "structure": "Split layout: content & image left, form right",
      "background": "background.gradientPrimary",
      "borderRadius": "xl",
      "padding": "3xl 2xl"
    },
    "features": {
      "structure": "Three-column grid",
      "background": "neutral.lightGray",
      "padding": "3xl 2xl"
    }
  },
  "components": {
    "buttons": {
      "primary": {
        "background": "secondary.yellow",
        "color": "neutral.black",
        "padding": "12px 24px",
        "borderRadius": "md",
        "fontWeight": 700,
        "fontSize": "16px",
        "hoverState": {
          "background": "secondary.yellowDark"
        }
      },
      "secondary": {
        "background": "transparent",
        "color": "neutral.white",
        "border": "2px solid neutral.white",
        "padding": "10px 22px",
        "borderRadius": "md",
        "hoverState": {
          "background": "neutral.white",
          "color": "primary.green"
        }
      }
    },
    "cards": {
      "bookingForm": {
        "background": "neutral.white",
        "borderRadius": "xl",
        "shadow": "card",
        "padding": "xl"
      },
      "feature": {
        "background": "transparent",
        "borderRadius": "none",
        "padding": "lg",
        "textAlign": "center"
      }
    },
    "forms": {
      "fields": {
        "input": {
          "borderRadius": "md",
          "border": "1px solid neutral.mediumGray",
          "padding": "12px 16px",
          "fontSize": "16px",
          "backgroundColor": "neutral.white",
          "focusState": {
            "borderColor": "primary.green",
            "boxShadow": "0 0 0 3px rgba(40, 167, 69, 0.2)"
          }
        },
        "select": {
          "appearance": "custom dropdown arrow",
          "icon": "chevron-down"
        },
        "dateInput": {
          "iconPosition": "right",
          "calendarIcon": "calendar"
        }
      },
      "labels": {
        "fontSize": "14px",
        "fontWeight": 500,
        "color": "neutral.black",
        "marginBottom": "sm"
      }
    },
    "navigation": {
      "style": "clean horizontal links",
      "fontSize": "16px",
      "fontWeight": 500,
      "color": "neutral.darkGray",
      "spacing": "xl between items",
      "hoverState": {
        "color": "primary.green"
      },
      "activeState": {
        "color": "primary.green",
        "fontWeight": 700
      }
    },
    "icons": {
      "style": "line icons",
      "strokeWidth": "2px",
      "size": {
        "regular": "24px",
        "feature": "48px"
      },
      "color": "primary.green"
    }
  },
  "imagery": {
    "productPhotos": {
      "style": "High-quality 3D render or professional photography",
      "lighting": "Clean, studio lighting",
      "background": "Transparent",
      "angle": "3/4 view"
    }
  },
  "animations": {
    "transitions": {
      "duration": "0.3s",
      "easing": "ease-in-out",
      "properties": ["color", "background-color", "transform", "box-shadow"]
    },
    "hoverEffects": {
      "buttons": "Subtle lift and background color change",
      "cards": "Subtle shadow increase"
    }
  },
  "responsiveDesign": {
    "breakpoints": {
      "mobile": "768px",
      "tablet": "1024px"
    },
    "heroAdaptation": {
      "mobile": "Stack vertically - content first, then form",
      "tablet": "Maintain side-by-side with adjusted proportions"
    },
    "featureGrid": {
      "mobile": "Single column",
      "tablet": "Three columns"
    }
  }
}


</style-guide>

