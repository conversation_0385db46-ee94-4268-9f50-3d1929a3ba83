TITLE: Starting Astro Development Server - pnpm
DESCRIPTION: This command starts the local development server for an Astro project using pnpm. It enables developers to view real-time changes to their site, facilitating an efficient development workflow.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/develop-and-build.mdx#_snippet_1

LANGUAGE: shell
CODE:
```
pnpm run dev
```

----------------------------------------

TITLE: Checking Node.js Version in Shell
DESCRIPTION: This command checks the currently installed version of Node.js on the system. It's used to verify compatibility with Astro's requirements, which support even-numbered Node.js versions (v18, v20, v22). If the command returns an error or an unsupported version, an update is required.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/tutorial/1-setup/1.mdx#_snippet_0

LANGUAGE: sh
CODE:
```
node -v

// Example output
v18.20.8
```

----------------------------------------

TITLE: Importing Astro DB Drizzle Client (TypeScript)
DESCRIPTION: This snippet shows the import statement for the `db` client from `astro:db`. This client provides a built-in Drizzle ORM interface for interacting with the Astro database, automatically configured for type-safe SQL queries.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/guides/astro-db.mdx#_snippet_19

LANGUAGE: ts
CODE:
```
import { db } from 'astro:db';
```

----------------------------------------

TITLE: Using Layout Components in Astro Pages
DESCRIPTION: This snippet illustrates how to import and utilize a layout component (`MySiteLayout.astro`) within an Astro page. Layouts help avoid repeating common HTML elements across multiple pages, promoting code reusability and maintainability.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/basics/astro-pages.mdx#_snippet_2

LANGUAGE: Astro
CODE:
```
---
import MySiteLayout from "../layouts/MySiteLayout.astro";
---
<MySiteLayout>
  <p>My page content, wrapped in a layout!</p>
</MySiteLayout>
```

----------------------------------------

TITLE: Displaying Queried Content in Astro Templates (Astro)
DESCRIPTION: This Astro component example shows how to integrate queried content into a template. It uses `getCollection()` to fetch all blog posts and then maps over them to create a list of links, accessing post data via the `data` property.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/guides/content-collections.mdx#_snippet_15

LANGUAGE: Astro
CODE:
```
---
import { getCollection } from 'astro:content';
const posts = await getCollection('blog');
---
<h1>My posts</h1>
<ul>
  {posts.map(post => (
    <li><a href={`/blog/${post.id}`}>{post.data.title}</a></li>
  ))}
</ul>
```

----------------------------------------

TITLE: Basic Astro Component Structure
DESCRIPTION: This snippet illustrates the two main parts of an Astro component: the Component Script, enclosed by code fences (---), where JavaScript logic resides, and the Component Template, which contains HTML and JavaScript expressions for rendering.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/basics/astro-components.mdx#_snippet_0

LANGUAGE: astro
CODE:
```
---
// Component Script (JavaScript)
---
<!-- Component Template (HTML + JS Expressions) -->
```

----------------------------------------

TITLE: Passing Props to an Astro Component
DESCRIPTION: This Astro page demonstrates how to pass `title` and `date` as attributes to the `Heading` component. These attributes are then accessible via `Astro.props` within the `Heading` component.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/reference/api-reference.mdx#_snippet_1

LANGUAGE: Astro
CODE:
```
---
```

LANGUAGE: Astro
CODE:
```
// src/pages/index.astro
import Heading from '../components/Heading.astro';
---
```

LANGUAGE: Astro
CODE:
```
<Heading title="My First Post" date="09 Aug 2022" />
```

----------------------------------------

TITLE: Starting Astro Development Server - npm
DESCRIPTION: This command starts the local development server for an Astro project using npm. It allows developers to see live updates to their site as they edit the code, providing an interactive development experience.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/develop-and-build.mdx#_snippet_0

LANGUAGE: shell
CODE:
```
npm run dev
```

----------------------------------------

TITLE: Loading Markdown Posts with Astro.glob() in Astro
DESCRIPTION: This Astro component demonstrates how to use `Astro.glob()` to asynchronously fetch and render a list of Markdown posts from a specified directory. It iterates through the returned array of post objects, accessing their `frontmatter` properties and `url` to display titles, descriptions, and links.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/reference/api-reference.mdx#_snippet_37

LANGUAGE: astro
CODE:
```
---\n// src/components/my-component.astro\nconst posts = await Astro.glob('../pages/post/*.md'); // returns an array of posts that live at ./src/pages/post/*.md\n---\n\n<div>\n{posts.slice(0, 3).map((post) => (\n  <article>\n    <h2>{post.frontmatter.title}</h2>\n    <p>{post.frontmatter.description}</p>\n    <a href={post.url}>Read more</a>\n  </article>\n))}\n</div>
```

----------------------------------------

TITLE: Starting Astro Development Server (npm, pnpm, yarn)
DESCRIPTION: This command initiates the Astro development server, providing a live, updating preview of the project in a browser. It's one of the most frequently used commands for local development.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/reference/cli-reference.mdx#_snippet_0

LANGUAGE: shell
CODE:
```
# start the development server
npx astro dev
```

LANGUAGE: shell
CODE:
```
# start the development server
pnpm astro dev
```

LANGUAGE: shell
CODE:
```
# start the development server
yarn astro dev
```

----------------------------------------

TITLE: Defining a Comment Table in Astro DB
DESCRIPTION: This example illustrates how to define a `Comment` table within `db/config.ts` using `defineTable` and `column` utilities. It configures two required text columns, `author` and `body`, making the table available for querying with full TypeScript support.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/guides/astro-db.mdx#_snippet_2

LANGUAGE: ts
CODE:
```
import { defineDb, defineTable, column } from 'astro:db';

const Comment = defineTable({
  columns: {
    author: column.text(),
    body: column.text()
  }
});

export default defineDb({
  tables: { Comment }
})
```

----------------------------------------

TITLE: Generating Static Paths with getStaticPaths() in Astro
DESCRIPTION: This snippet demonstrates the basic usage of `getStaticPaths()` in an Astro component (`src/pages/blog/[post].astro`) to define static routes. It returns an array of objects, each specifying a `params` object that maps to the dynamic segment (`[post]`) in the file path. This function is executed at build time to prerender pages.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/reference/routing-reference.mdx#_snippet_3

LANGUAGE: Astro
CODE:
```
---
// In 'server' mode, opt in to prerendering:
// export const prerender = true

export async function getStaticPaths() {
  return [
    // { params: { /* required */ }, props: { /* optional */ } },
    { params: { post: '1' } }, // [post] is the parameter
    { params: { post: '2' } }, // must match the file name
    // ...
  ];
}
---
<!-- Your HTML template here. -->
```

----------------------------------------

TITLE: Migrating Blog Post Fetching to getCollection() in Astro
DESCRIPTION: This snippet demonstrates updating `src/pages/blog.astro` to use `getCollection("blog")` for fetching all blog posts, replacing the older `import.meta.glob()` method. It shows the necessary import from `astro:content` and the updated `allPosts` variable assignment.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/tutorial/6-islands/4.mdx#_snippet_7

LANGUAGE: Astro
CODE:
```
---
import { getCollection } from "astro:content";
import BaseLayout from "../layouts/BaseLayout.astro";
import BlogPost from "../components/BlogPost.astro";

const pageTitle = "My Astro Learning Blog";
const allPosts = await getCollection("blog");
---
```

----------------------------------------

TITLE: Complete Tag Index Page Structure in Astro
DESCRIPTION: This comprehensive snippet presents the full content of the 'src/pages/tags/index.astro' file. It includes the frontmatter for importing the layout and dynamically collecting unique tags from blog posts, followed by the HTML structure with linked and styled tags, and the embedded CSS for presentation.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/tutorial/5-astro-api/3.mdx#_snippet_9

LANGUAGE: Astro
CODE:
```
---
import BaseLayout from '../../layouts/BaseLayout.astro';
const allPosts = Object.values(import.meta.glob('../posts/*.md', { eager: true }));
const tags = [...new Set(allPosts.map((post: any) => post.frontmatter.tags).flat())];
const pageTitle = "Tag Index";
---
<BaseLayout pageTitle={pageTitle}>
  <div class="tags">
    {tags.map((tag) => (
      <p class="tag"><a href={`/tags/${tag}`}>{tag}</a></p>
    ))}
  </div>
</BaseLayout>
<style>
  a {
    color: #00539F;
  }

  .tags {
    display: flex; 
    flex-wrap: wrap; 
  }

  .tag {
    margin: 0.25em;
    border: dotted 1px #a1a1a1;
    border-radius: .5em;
    padding: .5em 1em;
    font-size: 1.15em;
    background-color: #F8FCFD;
  }
</style>
```

----------------------------------------

TITLE: Retrieving Original Route from URL in Astro i18n (JavaScript)
DESCRIPTION: The `getRouteFromUrl` function, defined in `src/i18n/utils.ts`, extracts the original, untranslated route from a given URL. It parses the URL's pathname, determines the current language, and then looks up the corresponding original route in the `routes` mapping, effectively handling both default and translated paths. This function relies on `ui`, `defaultLang`, `showDefaultLang`, and `routes` from `ui.ts`, and assumes the existence of a `getLangFromUrl` helper.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/recipes/i18n.mdx#_snippet_18

LANGUAGE: JavaScript
CODE:
```
// src/i18n/utils.ts
import { ui, defaultLang, showDefaultLang, routes } from './ui';

export function getRouteFromUrl(url: URL): string | undefined {
  const pathname = new URL(url).pathname;
  const parts = pathname?.split('/');
  const path = parts.pop() || parts.pop();

  if (path === undefined) {
    return undefined;
  }
  
  const currentLang = getLangFromUrl(url);

  if (defaultLang === currentLang) {
    const route = Object.values(routes)[0];
    return route[path] !== undefined ? route[path] : undefined;
  }
  
  const getKeyByValue = (obj: Record<string, string>, value: string): string | undefined  => {
      return Object.keys(obj).find((key) => obj[key] === value);
  }

  const reversedKey = getKeyByValue(routes[currentLang], path);

  if (reversedKey !== undefined) {
    return reversedKey;
  }

  return undefined;
}
```

----------------------------------------

TITLE: Accessing Environment Variables with import.meta.env (JavaScript)
DESCRIPTION: This JavaScript snippet illustrates how to access environment variables within Astro components and scripts using `import.meta.env`. It shows examples for both server-side (SSR) and client-side access, demonstrating how `DB_PASSWORD` might be used on the server and `PUBLIC_POKEAPI` on the client.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/guides/environment-variables.mdx#_snippet_13

LANGUAGE: js
CODE:
```
// When import.meta.env.SSR === true
const data = await db(import.meta.env.DB_PASSWORD);

// When import.meta.env.SSR === false
const data = fetch(`${import.meta.env.PUBLIC_POKEAPI}/pokemon/squirtle`);
```

----------------------------------------

TITLE: Setting Flotiq API Key in .env
DESCRIPTION: This snippet shows how to set the Flotiq read-only API key as an environment variable in the `.env` file at the root of an Astro project. This key is essential for authenticating requests to the Flotiq API.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/guides/cms/flotiq.mdx#_snippet_0

LANGUAGE: ini
CODE:
```
FLOTIQ_API_KEY=__YOUR_FLOTIQ_API_KEY__
```

----------------------------------------

TITLE: Defining Astro CLI Scripts in `package.json`
DESCRIPTION: This snippet defines common Astro CLI commands (`dev`, `build`, `preview`) as shorthand scripts within the `package.json` file. These scripts allow users to run `astro` commands using familiar package manager commands like `npm run dev`, simplifying project workflows and ensuring consistent command execution across different environments.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/reference/cli-reference.mdx#_snippet_5

LANGUAGE: json
CODE:
```
{
  "scripts": {
    "dev": "astro dev",
    "build": "astro build",
    "preview": "astro preview"
  }
}
```

----------------------------------------

TITLE: Optimización de Imágenes Locales con el Componente Astro Image
DESCRIPTION: Este ejemplo ilustra cómo importar una imagen local y renderizarla utilizando el componente `<Image />` de `astro:assets`. El componente optimiza la imagen y establece automáticamente los atributos `width` y `height` para mejorar el Desplazamiento Acumulativo de Diseño (CLS). El atributo `alt` es obligatorio.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/es/guides/images.mdx#_snippet_1

LANGUAGE: astro
CODE:
```
---
// Importa el componente Image y la imagen 
import { Image } from 'astro:assets';
import myImage from '../assets/my_image.png'; // La imagen es 1600x900
---

<!-- `alt` es obligatorio en el componente Imagen -->
<Image src={myImage} alt="Una descripción de mi imagen." />
```

----------------------------------------

TITLE: Creating a New Astro Project
DESCRIPTION: This command initializes a new Astro project using the specified package manager. It launches an interactive CLI wizard that guides the user through setting up the project directory, selecting a starter template, and configuring initial settings.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/install-and-setup.mdx#_snippet_0

LANGUAGE: shell
CODE:
```
npm create astro@latest
```

LANGUAGE: shell
CODE:
```
pnpm create astro@latest
```

LANGUAGE: shell
CODE:
```
yarn create astro
```

----------------------------------------

TITLE: Defining Dynamic Routes with `getStaticPaths()` in Astro
DESCRIPTION: Shows how to define dynamic routes in Astro's static (SSG) mode using `getStaticPaths()`. This function returns an array of objects, each with a `params` property, to generate multiple pages based on a dynamic segment in the filename. The `Astro.params` object is used to access the dynamic parameter within the page.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/guides/routing.mdx#_snippet_2

LANGUAGE: Astro
CODE:
```
---
export function getStaticPaths() {
  return [
    { params: { dog: "clifford" }},
    { params: { dog: "rover" }},
    { params: { dog: "spot" }},
  ];
}

const { dog } = Astro.params;
---
<div>Good dog, {dog}!</div>
```

----------------------------------------

TITLE: Implementing Theme Toggle with Inline JavaScript in Astro
DESCRIPTION: This snippet demonstrates how to add client-side interactivity to an Astro component using an inline `<script>` tag. It initializes the theme based on `localStorage` or system preference, applies it to the `document.documentElement`, and provides a click handler to toggle the theme between 'dark' and 'light' modes, persisting the choice in `localStorage`. It requires an HTML element with `id='themeToggle'` to attach the event listener.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/tutorial/6-islands/2.mdx#_snippet_3

LANGUAGE: Astro
CODE:
```
<style>
  .sun { fill: black; }
  .moon { fill: transparent; }

  :global(.dark) .sun { fill: transparent; }
  :global(.dark) .moon { fill: white; }
</style>

<script is:inline>
  const theme = (() => {
    const localStorageTheme = localStorage?.getItem("theme") ?? '';
    if (['dark', 'light'].includes(localStorageTheme)) {
      return localStorageTheme;
    }
    if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'dark';
    }
      return 'light';
  })();
      
  if (theme === 'light') {
    document.documentElement.classList.remove('dark');
  } else {
    document.documentElement.classList.add('dark');
  }

  window.localStorage.setItem('theme', theme);

  const handleToggleClick = () => {
    const element = document.documentElement;
    element.classList.toggle("dark");
    
    const isDark = element.classList.contains("dark");
    localStorage.setItem("theme", isDark ? "dark" : "light");
  }

  document.getElementById("themeToggle")?.addEventListener("click", handleToggleClick);
</script>
```

----------------------------------------

TITLE: Using Variables in Astro Components
DESCRIPTION: Demonstrates how to declare a local JavaScript variable in the Astro component's frontmatter and inject its value into the HTML template using curly braces. This shows basic dynamic content rendering.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/reference/astro-syntax.mdx#_snippet_0

LANGUAGE: Astro
CODE:
```
--- const name = "Astro"; ---\n<div>\n  <h1>Hello {name}!</h1>  <!-- Outputs <h1>Hello Astro!</h1> -->\n</div>
```

----------------------------------------

TITLE: Implementing Nested Pagination with getStaticPaths in Astro
DESCRIPTION: This Astro snippet demonstrates how to implement nested pagination using `getStaticPaths` to create dynamic routes based on tags. It iterates through a list of tags, filters posts for each tag, and then uses `paginate()` to generate a paginated result set, ensuring the `tag` parameter is passed for correct routing. This allows for URLs like `/red/1` and `/blue/1`.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/guides/routing.mdx#_snippet_21

LANGUAGE: Astro
CODE:
```
// src/pages/[tag]/[page].astro
export function getStaticPaths({ paginate }) {
  const allTags = ["red", "blue", "green"];
  const allPosts = Object.values(import.meta.glob("../pages/post/*.md", { eager: true }));
  // For every tag, return a `paginate()` result.
  // Make sure that you pass `{ params: { tag }}` to `paginate()`
  // so that Astro knows which tag grouping the result is for.
  return allTags.flatMap((tag) => {
    const filteredPosts = allPosts.filter((post) => post.frontmatter.tag === tag);
    return paginate(filteredPosts, {
      params: { tag },
      pageSize: 10
    });
  });
}

const { page } = Astro.props;
const params = Astro.params;
```

----------------------------------------

TITLE: Hydrating Interactive Components in Astro with Client Directives
DESCRIPTION: This example illustrates how to make framework components interactive in Astro using client:* directives. It shows client:load for immediate hydration, client:visible for hydration when the component enters the viewport, and client:only="svelte" for client-side only rendering, preventing server-side rendering for specific frameworks.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/guides/framework-components.mdx#_snippet_1

LANGUAGE: Astro
CODE:
```
---
// Example: hydrating framework components in the browser.
import InteractiveButton from '../components/InteractiveButton.jsx';
import InteractiveCounter from '../components/InteractiveCounter.jsx';
import InteractiveModal from '../components/InteractiveModal.svelte';
---
<!-- This component's JS will begin importing when the page loads -->
<InteractiveButton client:load />

<!-- This component's JS will not be sent to the client until
the user scrolls down and the component is visible on the page -->
<InteractiveCounter client:visible />

<!-- This component won't render on the server, but will render on the client when the page loads -->
<InteractiveModal client:only="svelte" />
```

----------------------------------------

TITLE: Typing Component Props in Astro Components (Astro)
DESCRIPTION: Shows how to define component props using a TypeScript `Props` interface within an Astro component's frontmatter. This enables TypeScript support for props, providing type checking and autocompletion when the component is used in other templates.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/guides/typescript.mdx#_snippet_13

LANGUAGE: Astro
CODE:
```
---
interface Props {
  name: string;
  greeting?: string;
}

const { greeting = "Hello", name } = Astro.props;
---
<h2>{greeting}, {name}!</h2>
```

----------------------------------------

TITLE: Creating a Markdown Blog Post with Frontmatter in Astro
DESCRIPTION: This snippet illustrates how to structure a Markdown blog post (`.md`) in an Astro project. It includes a YAML frontmatter block at the top for defining metadata such as title, publication date, description, author, image details, and tags, which Astro can use to enhance the site. The content below the frontmatter is standard Markdown that will be converted to HTML.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/tutorial/2-pages/2.mdx#_snippet_0

LANGUAGE: markdown
CODE:
```
---
title: 'My First Blog Post'
pubDate: 2022-07-01
description: 'This is the first post of my new Astro blog.'
author: 'Astro Learner'
image:
    url: 'https://docs.astro.build/assets/rose.webp' 
    alt: 'The Astro logo on a dark background with a pink glow.'
tags: ["astro", "blogging", "learning in public"]
---
# My First Blog Post

Published on: 2022-07-01

Welcome to my _new blog_ about learning Astro! Here, I will share my learning journey as I build a new website.

## What I've accomplished

1. **Installing Astro**: First, I created a new Astro project and set up my online accounts.

2. **Making Pages**: I then learned how to make pages by creating new `.astro` files and placing them in the `src/pages/` folder.

3. **Making Blog Posts**: This is my first blog post! I now have Astro pages and Markdown posts!

## What's next

I will finish the Astro tutorial, and then keep adding more posts. Watch this space for more to come.
```

----------------------------------------

TITLE: Creating Astro Project from Template (Shell)
DESCRIPTION: This snippet illustrates how to initialize a new Astro project based on an official example or a GitHub repository's `main` branch using the `create astro` command with the `--template` argument. It includes examples for npm, pnpm, and yarn, and notes how to specify a different branch.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/install-and-setup.mdx#_snippet_3

LANGUAGE: shell
CODE:
```
# create a new project with an official example
npm create astro@latest -- --template <example-name>

# create a new project based on a GitHub repository’s main branch
npm create astro@latest -- --template <github-username>/<github-repo>
```

LANGUAGE: shell
CODE:
```
# create a new project with an official example
pnpm create astro@latest --template <example-name>

# create a new project based on a GitHub repository’s main branch
pnpm create astro@latest --template <github-username>/<github-repo>
```

LANGUAGE: shell
CODE:
```
# create a new project with an official example
yarn create astro --template <example-name>

# create a new project based on a GitHub repository’s main branch
yarn create astro --template <github-username>/<github-repo>
```

----------------------------------------

TITLE: Defining a Basic Astro Layout Component
DESCRIPTION: This Astro layout component demonstrates the fundamental structure for pages, including required `<html>`, `<head>`, and `<body>` tags. It uses `<slot />` to inject page-specific content, allowing for consistent templating across multiple pages.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/guides/migrate-to-astro/from-nextjs.mdx#_snippet_3

LANGUAGE: Astro
CODE:
```
---
---
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="viewport" content="width=device-width" />
		<meta name="generator" content={Astro.generator} />
		<title>Astro</title>
	</head>
	<body>
    <!-- Wrap the slot element with your existing layout templating -->
		<slot />
	</body>
</html>
```

----------------------------------------

TITLE: Accessing Dynamic Route Parameters with Astro.params
DESCRIPTION: This Astro page demonstrates how to use `getStaticPaths` to define static paths for dynamic routes. The `id` parameter, matched from the file path, is then accessed via `Astro.params` and rendered in the component.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/reference/api-reference.mdx#_snippet_4

LANGUAGE: Astro
CODE:
```
---
```

LANGUAGE: Astro
CODE:
```
export function getStaticPaths() {
  return [
    { params: { id: '1' } },
    { params: { id: '2' } },
    { params: { id: '3' } }
  ];
}
```

LANGUAGE: Astro
CODE:
```
const { id } = Astro.params;
---
```

LANGUAGE: Astro
CODE:
```
<h1>{id}</h1>
```

----------------------------------------

TITLE: Implementing Auth.js Sign-in/Sign-out Buttons in Astro
DESCRIPTION: This Astro component example demonstrates how to create client-side sign-in and sign-out buttons using the `auth-astro/client` module. It shows how to import `signIn` and `signOut` functions and attach them to button click events for user authentication flow.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/guides/authentication.mdx#_snippet_9

LANGUAGE: astro
CODE:
```
--- 
import Layout from 'src/layouts/Base.astro';
---
<Layout>
  <button id="login">Login</button>
  <button id="logout">Logout</button>

  <script>
    const { signIn, signOut } = await import("auth-astro/client")
    document.querySelector("#login").onclick = () => signIn("github")
    document.querySelector("#logout").onclick = () => signOut()
  </script>
</Layout>
```

----------------------------------------

TITLE: Example Astro Project File Tree Structure
DESCRIPTION: This snippet illustrates a common directory and file structure for an Astro project, showcasing the organization of source code, public assets, and configuration files. It highlights typical locations for pages, components, layouts, styles, and content.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/basics/project-structure.mdx#_snippet_0

LANGUAGE: File System Structure
CODE:
```
- public/
  - robots.txt
  - favicon.svg
  - my-cv.pdf
- src/
    - blog/
      - post1.md
      - post2.md
      - post3.md
  - components/
    - Header.astro
    - Button.jsx
  - images/
    - image1.jpg
    - image2.jpg
    - image3.jpg
  - layouts/
    - PostLayout.astro
  - pages/
    - posts/
      - [post].astro
    - about.astro
    - **index.astro**
    - rss.xml.js
  - styles/
    - global.css
  - content.config.ts
- astro.config.mjs
- package.json
- tsconfig.json
```

----------------------------------------

TITLE: Inserting Comment Data from Form POST (Astro)
DESCRIPTION: This Astro page snippet handles a form POST request to insert new data into the `Comment` table. It parses `formData` for `author` and `body` values, inserts them into the database using `db.insert()`, and then re-renders the updated list of comments. Requires on-demand rendering and an adapter.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/guides/astro-db.mdx#_snippet_21

LANGUAGE: astro
CODE:
```
---
// src/pages/index.astro
import { db, Comment } from 'astro:db';

if (Astro.request.method === 'POST') {
  // Parse form data
  const formData = await Astro.request.formData();
  const author = formData.get('author');
  const body = formData.get('body');
  if (typeof author === 'string' && typeof body === 'string') {
    // Insert form data into the Comment table
    await db.insert(Comment).values({ author, body });
  }
}

// Render the new list of comments on each request
const comments = await db.select().from(Comment);
---

<form method="POST" style="display: grid">
	<label for="author">Author</label>
	<input id="author" name="author" />

	<label for="body">Body</label>
	<textarea id="body" name="body"></textarea>

	<button type="submit">Submit</button>
</form>

<!-- Render `comments` -->
```

----------------------------------------

TITLE: Generating Static Paths with getStaticPaths in Astro
DESCRIPTION: This snippet demonstrates how to use getStaticPaths() in Astro to generate static pages from a content collection. It fetches all entries from the 'blog' collection, creates a unique path for each using its 'id', and passes the entire collection entry as a prop to the page template for rendering. This is suitable for static site generation.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/guides/content-collections.mdx#_snippet_22

LANGUAGE: astro
CODE:
```
---
import { getCollection, render } from 'astro:content';
// 1. Generate a new path for every collection entry
export async function getStaticPaths() {
  const posts = await getCollection('blog');
  return posts.map(post => ({
    params: { id: post.id },
    props: { post },
  }));
}
// 2. For your template, you can get the entry directly from the prop
const { post } = Astro.props;
const { Content } = await render(post);
---
<h1>{post.data.title}</h1>
<Content />
```

----------------------------------------

TITLE: Importing and Rendering Markdown in Astro Components
DESCRIPTION: This Astro component illustrates how to import a single Markdown file and multiple Markdown files using `import.meta.glob()`. It then demonstrates accessing and rendering frontmatter properties (e.g., `greatPost.frontmatter.title`) and dynamically generating a list of posts with links from the imported Markdown data.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/guides/markdown-content.mdx#_snippet_1

LANGUAGE: astro
CODE:
```
---
import * as greatPost from './posts/great-post.md';
const posts = Object.values(import.meta.glob('./posts/*.md', { eager: true }));
---

<p>{greatPost.frontmatter.title}</p>
<p>Written by: {greatPost.frontmatter.author}</p>

<p>Post Archive:</p>
<ul>
  {posts.map(post => <li><a href={post.url}>{post.frontmatter.title}</a></li>)}
</ul>
```

----------------------------------------

TITLE: Handling Multiple HTTP Methods in Astro API (TypeScript)
DESCRIPTION: This TypeScript example demonstrates how to define separate functions for different HTTP methods (`GET`, `POST`, `DELETE`) within a single API route. It also includes an `ALL` function, which acts as a fallback for any method that doesn't have a specific handler, providing flexibility in API design.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/guides/endpoints.mdx#_snippet_7

LANGUAGE: ts
CODE:
```
export const GET: APIRoute = ({ params, request }) => {
  return new Response(
    JSON.stringify({
      message: "This was a GET!"
    }),
  );
};

export const POST: APIRoute = ({ request }) => {
  return new Response(
    JSON.stringify({
      message: "This was a POST!"
    }),
  );
};

export const DELETE: APIRoute = ({ request }) => {
  return new Response(
    JSON.stringify({
      message: "This was a DELETE!"
    }),
  );
};

export const ALL: APIRoute = ({ request }) => {
  return new Response(
    JSON.stringify({
      message: `This was a ${request.method}!`
    }),
  );
};
```

----------------------------------------

TITLE: Performing Server-Side Redirects with context.redirect (TypeScript)
DESCRIPTION: This TypeScript snippet demonstrates how to perform a server-side redirect using the `redirect` function from `APIContext` in an Astro API route. It checks user authentication and redirects to a login page with a `302` status code if the user is not logged in. This method supports custom status codes for dynamic routes.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/reference/api-reference.mdx#_snippet_19

LANGUAGE: ts
CODE:
```
import type { APIContext } from 'astro';

export function GET({ redirect, request }: APIContext) {
  const cookie = request.headers.get('cookie');
  if (!isLoggedIn(cookie)) {
    return redirect('/login', 302);
  } else {
    // return user information
  }
}
```

----------------------------------------

TITLE: Defining Action with Discriminated Union for Form Input Validation
DESCRIPTION: This advanced example demonstrates using `z.discriminatedUnion` within `defineAction` to validate form inputs based on a specific field (`type`). It enables the `changeUser` action to handle different input schemas for 'create' and 'update' operations, ensuring type safety and proper data parsing for complex form submissions with varying data structures.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/reference/modules/astro-actions.mdx#_snippet_2

LANGUAGE: ts
CODE:
```
import { defineAction } from 'astro:actions';
import { z } from 'astro:schema';

export const server = {
  changeUser: defineAction({
    accept: 'form',
    input: z.discriminatedUnion('type', [
      z.object({
        // Matches when the `type` field has the value `create`
        type: z.literal('create'),
        name: z.string(),
        email: z.string().email()
      }),
      z.object({
        // Matches when the `type` field has the value `update`
        type: z.literal('update'),
        id: z.number(),
        name: z.string(),
        email: z.string().email()
      })
    ]),
    async handler(input) {
      if (input.type === 'create') {
        // input is { type: 'create', name: string, email: string }
      } else {
        // input is { type: 'update', id: number, name: string, email: string }
      }
    }
  })
};
```

----------------------------------------

TITLE: Defining a Server Action with Zod Input Validation in Astro
DESCRIPTION: This example illustrates using `defineAction()` to create a server-side action named `getGreeting`. It configures an `input` schema with Zod to validate that the `name` parameter is a string, and defines a `handler` function that processes the validated input to return a personalized greeting. This setup ensures type-safe and validated input handling for the action.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/reference/modules/astro-actions.mdx#_snippet_1

LANGUAGE: ts
CODE:
```
import { defineAction } from 'astro:actions';
import { z } from 'astro:schema';

export const server = {
  getGreeting: defineAction({
    input: z.object({
      name: z.string()
    }),
    handler: async (input, context) => {
      return `Hello, ${input.name}!`
    }
  })
};
```

----------------------------------------

TITLE: Creating a New Astro Project with Blog Template (PNPM)
DESCRIPTION: This command initializes a new Astro project using the pnpm package manager. It utilizes the `create astro` command to scaffold a project based on the official 'blog' starter template, offering an efficient way to set up a blog-focused Astro site. The `--template blog` argument specifies the desired starter.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/guides/migrate-to-astro/from-sveltekit.mdx#_snippet_1

LANGUAGE: shell
CODE:
```
pnpm create astro@latest --template blog
```

----------------------------------------

TITLE: Installing Astro with pnpm (Shell)
DESCRIPTION: This command installs Astro as a local project dependency using pnpm. It's important to install Astro locally, not globally, for proper project management.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/install-and-setup.mdx#_snippet_9

LANGUAGE: shell
CODE:
```
pnpm add astro
```

----------------------------------------

TITLE: Importing Image and Asset Components
DESCRIPTION: Demonstrates how to import core image and asset components and helper functions from `astro:assets` for use in Astro projects.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/reference/modules/astro-assets.mdx#_snippet_0

LANGUAGE: js
CODE:
```
import {
  Image,
  Picture,
  getImage,
  inferRemoteSize,
 } from 'astro:assets';
```

----------------------------------------

TITLE: Importing CSS Files
DESCRIPTION: Illustrates how to import a CSS file into an application. Importing a CSS file automatically adds its styles to the page, providing a straightforward way to include global or component-specific stylesheets without requiring additional configuration.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/ko/guides/imports.mdx#_snippet_5

LANGUAGE: js
CODE:
```
// 페이지에 'style.css' 불러오기 및 삽입
import './style.css';
```

----------------------------------------

TITLE: Handling Multiple Dynamic Parameters in Astro Routes
DESCRIPTION: Demonstrates how to define dynamic routes with multiple parameters in Astro. The `getStaticPaths()` function must include all defined parameters in its `params` objects to generate the corresponding routes.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/guides/routing.mdx#_snippet_3

LANGUAGE: Astro
CODE:
```
---
export function getStaticPaths() {
  return [
    { params: { lang: "en", version: "v1" }},
    { params: { lang: "fr", version: "v2" }},
  ];
}

const { lang, version } = Astro.params;
---
```

----------------------------------------

TITLE: Submitting Form Data Programmatically with Astro Actions (Astro)
DESCRIPTION: This Astro snippet adds a client-side `<script>` to the HTML form. It imports `actions` and `navigate` from Astro. The script prevents the default form submission, captures form data using `FormData`, calls the `actions.newsletter()` server action, and redirects to a confirmation page upon successful submission (no error).
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/guides/actions.mdx#_snippet_17

LANGUAGE: Astro
CODE:
```
<form>
  <label for="email">E-mail</label>
  <input id="email" required type="email" name="email" />
  <label>
    <input required type="checkbox" name="terms">
    I agree to the terms of service
  </label>
  <button>Sign up</button>
</form>

<script>
  import { actions } from 'astro:actions';
  import { navigate } from 'astro:transitions/client';

  const form = document.querySelector('form');
  form?.addEventListener('submit', async (event) => {
    event.preventDefault();
    const formData = new FormData(form);
    const { error } = await actions.newsletter(formData);
    if (!error) navigate('/confirmation');
  })
</script>
```

----------------------------------------

TITLE: Importing All Markdown Posts in Astro
DESCRIPTION: This code demonstrates how to use Astro's `import.meta.glob` feature with `eager: true` to import all Markdown files from a specified directory (`../posts/*.md`). The `Object.values()` method is used to get an array of the imported modules, making their frontmatter data accessible for further processing.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/tutorial/5-astro-api/3.mdx#_snippet_3

LANGUAGE: Astro
CODE:
```
---
import BaseLayout from '../../layouts/BaseLayout.astro';
const allPosts = Object.values(import.meta.glob('../posts/*.md', { eager: true }));
const pageTitle = "Tag Index";
---

```

----------------------------------------

TITLE: Defining Static Paths with getStaticPaths in Astro
DESCRIPTION: This Astro code snippet illustrates the correct implementation of `getStaticPaths` for generating static routes. It demonstrates returning an array where each object includes a `params` property, mapping dynamic segments (like `[id]`) to specific values (e.g., `id: '1'`), which Astro uses to create a static page such as `site.com/blog/1`.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/reference/errors/get-static-paths-expected-params.mdx#_snippet_0

LANGUAGE: Astro
CODE:
```
---
export async function getStaticPaths() {
	return [
		{ params: { id: '1' } }
	];
}---

```

----------------------------------------

TITLE: Replacing HTML with Navigation Component in Astro
DESCRIPTION: This snippet shows the replacement of duplicated HTML navigation links with the imported `<Navigation />` component in `index.astro`. This refactoring simplifies the page's markup, making it more maintainable and demonstrating the reusability of Astro components.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/tutorial/3-components/1.mdx#_snippet_2

LANGUAGE: Astro
CODE:
```
<a href="/">Home</a>
<a href="/about/">About</a>
<a href="/blog/">Blog</a>
<Navigation />
```

----------------------------------------

TITLE: Enabling Client-Side Interactivity with client:load in Astro
DESCRIPTION: This Astro component snippet demonstrates how to make a UI component interactive using the `client:load` directive. By adding `client:load`, Astro automatically bundles and loads the component's client-side JavaScript immediately upon page load, transforming it into an interactive 'island' while the rest of the page remains static. This allows for selective hydration, optimizing performance by only loading JavaScript for explicitly marked interactive elements.
SOURCE: https://github.com/withastro/docs/blob/main/src/content/docs/en/concepts/islands.mdx#_snippet_1

LANGUAGE: Astro
CODE:
```
<!-- This component is now interactive on the page! 
     The rest of your website remains static. -->
<MyReactComponent client:load />
```