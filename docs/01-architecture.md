## Launch Features (MVP)

#### User, Admin & Profile Core

This foundational module handles identity, roles, and content oversight for the entire platform. It establishes who can do what, from basic user actions to administrative control.

  * User registration/login (Renter, Owner roles).
  * Admin role with access to a basic moderation dashboard.
  * Users can manage their core profile details.
  * Admins can view/manage users and review flagged content.

##### Tech Involved

  * **Frontend:** Astro/React for forms and the admin dashboard.
  * **Backend & Auth:** Pocketbase `users` collection with a `role` field (`renter`, `owner`, `admin`).

##### Main Requirements

  * Collection rules in Pocketbase to protect routes and actions based on user role.
  * A secure area in the frontend application that is only visible to users with the `admin` role.

#### Venue Marketplace (Listing & Search)

This is the public-facing core of the platform, focused on how Owners present their venues and how Renters discover them. It integrates a powerful search experience and the required native virtual tour functionality.

  * Owners can create/edit listings with details, standard photos, and 360° photos.
  * A native, interactive 360° photo viewer is embedded directly on the listing page.
  * Renters use a high-speed, typo-tolerant search bar to find venues.
  * A mechanism for users (Renters or Owners) to flag a listing for administrative review.

##### Tech Involved

  * **Frontend:** React components for the search interface and the native 360° viewer (using a library like **Pannellum** or **Photo Sphere Viewer**).
  * **Search:** A dedicated **Meilisearch** instance. The frontend search component will query Meilisearch directly.
  * **Backend:** Pocketbase for storing the canonical listing data.

##### Main Requirements

  * The 360° viewer must be performant and work with equirectangular images uploaded by owners.
  * A `flagged_content` collection in Pocketbase to store moderation flags submitted by users.

#### Transaction Lifecycle (Booking, Payments & Payouts)

This comprehensive feature manages the entire commercial flow, from initial inquiry to final payout, ensuring secure and reliable transactions for both parties.

  * Renters can request to book a venue for a specific date range.
  * Owners can approve/deny requests. Upon approval, the renter is prompted to pay.
  * The system checks for date-range conflicts to prevent double-bookings.
  * Owners can upload a static PDF rental agreement per listing for renters to view.
  * Secure payment processing via Paystack for Renters.
  * **Automated payouts** to the Property Owner's linked bank account after a successful booking, minus any platform fees.
  * A real-time messaging system for communication tied to each booking.

##### Tech Involved

  * **Frontend:** React for booking forms, messaging UI, and payment flow.
  * **Payment & Payouts:** Paystack API (Payments for intake, Transfers for payouts).
  * **Backend:** Pocketbase `bookings` collection with `start_date`, `end_date`, `status`, and `payment_details`. Realtime subscriptions for messaging.
  * **Payout Service:** The Deno service will manage payout logic, securely interacting with the Paystack Transfers API.

##### Main Requirements

  * The `bookings` collection must have an API rule that executes a query to check for overlapping `start_date` and `end_date` for the same `venue` before creating a new `pending` booking.
  * The Deno service must securely store owner bank details (as Paystack recipients) and have logic to calculate payout amounts and schedules.

#### System Services

These are the critical backend processes that support the main features but are not directly interfaces for the user.

  * **Paystack Webhook & Payout Service:** The Deno service listens for payment success webhooks from Paystack to update booking statuses. It also contains the scheduled or event-triggered logic to initiate payouts to owners.
  * **Pocketbase-to-Meilisearch Sync Service:** A lightweight service (can be part of the Deno app or a separate script) that listens for changes in the Pocketbase `venues` collection (creates, updates, deletes) and pushes those changes to the Meilisearch instance to keep the search index current.

##### Tech Involved

  * **Sync/Webhook Host:** A container running the Deno application on **Fly.io** or **Railway**.
  * **SDKs:** Pocketbase and Meilisearch JS/TS SDKs.

##### Main Requirements

  * The sync service must be reliable and handle potential failures gracefully (e.g., retries).
  * The webhook endpoint must be secured to only accept requests from Paystack.

## Future Features (Post-MVP)

#### Enhanced Legal & E-Signature

  * Transition from static PDF uploads to dynamic, templated legal agreements.
  * Integrate a third-party e-signature service like **SignWell** to allow for legally binding signatures directly on the platform.

#### Advanced Analytics & Reporting

  * Provide a dashboard for Property Owners with insights on listing views, booking rates, and revenue trends.
  * Utilize the data from **PostHog** to generate these reports.

#### Tiered Subscriptions & Feature Gating

  * Introduce subscription plans for Property Owners (e.g., a "Pro" tier for more photo uploads or higher search placement).
  * This would require more complex logic in both Pocketbase (user roles/permissions) and Paystack (recurring billing).

## System Diagram

```svg
<svg xmlns="http://www.w3.org/2000/svg" width="900" height="700" viewBox="0 0 900 700" style="background-color: #f8fafc; font-family: 'Poppins', sans-serif;">
    <!-- Definitions -->
    <defs>
        <style>
            .label { font-size: 14px; font-weight: 600; fill: #1e293b; }
            .desc { font-size: 11px; fill: #475569; }
            .arrow { marker-end: url(#arrowhead); stroke: #64748b; stroke-width: 1.5; fill: none; }
            .arrow-dashed { marker-end: url(#arrowhead); stroke: #64748b; stroke-width: 1.5; stroke-dasharray: 4 4; fill: none; }
        </style>
        <marker id="arrowhead" viewBox="0 0 10 10" refX="8" refY="5" markerWidth="6" markerHeight="6" orient="auto-start-reverse">
            <path d="M 0 0 L 10 5 L 0 10 z" fill="#64748b" />
        </marker>
        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur in="SourceAlpha" stdDeviation="3"/>
            <feOffset dx="2" dy="2" result="offsetblur"/>
            <feMerge>
                <feMergeNode/>
                <feMergeNode in="SourceGraphic"/>
            </feMerge>
        </filter>
    </defs>

    <!-- Main Containers -->
    <rect x="20" y="20" width="860" height="660" rx="20" fill="white" stroke="#e2e8f0" />
    <text x="450" y="50" text-anchor="middle" font-size="20" font-weight="bold" fill="#0f172a">MVP System Architecture (Revised)</text>
    
    <!-- User Client -->
    <g transform="translate(40, 310)">
        <rect x="0" y="0" width="160" height="90" rx="10" fill="#f0fdf4" stroke="#86efac" filter="url(#shadow)" />
        <text x="80" y="35" class="label" text-anchor="middle">User/Admin Client</text>
        <text x="80" y="55" class="desc" text-anchor="middle">Renter / Owner / Admin</text>
        <text x="80" y="70" class="desc" text-anchor="middle">(Web Browser)</text>
    </g>

    <!-- Frontend -->
    <g transform="translate(250, 310)">
        <rect x="0" y="0" width="180" height="110" rx="10" fill="#eff6ff" stroke="#93c5fd" filter="url(#shadow)" />
        <text x="90" y="25" class="label" text-anchor="middle">Frontend</text>
        <text x="90" y="45" class="desc" text-anchor="middle">Astro + React</text>
        <text x="90" y="60" class="desc" text-anchor="middle">• 360° Viewer (Pannellum)</text>
        <text x="90" y="75" class="desc" text-anchor="middle">• Search UI</text>
        <text x="90" y="90" class="desc" text-anchor="middle">(Hosted on Vercel/Netlify)</text>
    </g>

    <!-- Backend Infrastructure -->
    <g transform="translate(480, 80)">
        <rect x="0" y="0" width="380" height="420" rx="10" fill="none" stroke="#cbd5e1" stroke-dasharray="5 5" />
        <text x="190" y="-10" text-anchor="middle" font-size="12" fill="#475569">Backend Infrastructure (Hosted on Fly.io/Railway)</text>
        <!-- Pocketbase -->
        <g transform="translate(80, 30)">
            <rect x="0" y="0" width="220" height="110" rx="10" fill="#fffbeb" stroke="#fde047" filter="url(#shadow)" />
            <text x="110" y="30" class="label" text-anchor="middle">Pocketbase (BaaS)</text>
            <text x="110" y="50" class="desc" text-anchor="middle">• Database / Auth</text>
            <text x="110" y="65" class="desc" text-anchor="middle">• File Storage (Images, PDFs)</text>
            <text x="110" y="80" class="desc" text-anchor="middle">• Realtime API (Chat)</text>
            <text x="110" y="95" class="desc" text-anchor="middle">• Admin &amp; Data Schemas</text>
        </g>
        <!-- Meilisearch -->
        <g transform="translate(20, 160)">
            <rect x="0" y="0" width="160" height="90" rx="10" fill="#fef2f2" stroke="#fca5a5" filter="url(#shadow)" />
            <text x="80" y="30" class="label" text-anchor="middle">Meilisearch</text>
            <text x="80" y="50" class="desc" text-anchor="middle">(Search Service)</text>
            <text x="80" y="65" class="desc" text-anchor="middle">Provides Search API</text>
        </g>
        <!-- Deno Service -->
        <g transform="translate(200, 160)">
            <rect x="0" y="0" width="160" height="230" rx="10" fill="#f0f9ff" stroke="#7dd3fc" filter="url(#shadow)" />
            <text x="80" y="30" class="label" text-anchor="middle">Deno Service</text>
            <text x="80" y="55" class="desc" text-anchor="middle">**Sync Service**</text>
            <text x="80" y="70" class="desc" text-anchor="middle">(Pocketbase → Meilisearch)</text>
            <text x="80" y="100" class="desc" text-anchor="middle">**Webhook Service**</text>
            <text x="80" y="115" class="desc" text-anchor="middle">(Receives Paystack Events)</text>
            <text x="80" y="145" class="desc" text-anchor="middle">**Payout Service**</text>
            <text x="80" y="160" class="desc" text-anchor="middle">(Triggers Paystack Transfers)</text>
             <text x="80" y="190" class="desc" text-anchor="middle">**Secure Ops**</text>
             <text x="80" y="205" class="desc" text-anchor="middle">(e.g. payout calculations)</text>
        </g>
    </g>
    
    <!-- 3rd Party Services -->
    <g transform="translate(480, 520)">
         <rect x="0" y="0" width="380" height="140" rx="10" fill="none" stroke="#cbd5e1" stroke-dasharray="5 5" />
        <text x="190" y="-10" text-anchor="middle" font-size="12" fill="#475569">Third-Party Services</text>
         <!-- Paystack -->
        <g transform="translate(20, 20)">
            <rect x="0" y="0" width="160" height="100" rx="10" fill="#fdf2f8" stroke="#f9a8d4" filter="url(#shadow)" />
            <text x="80" y="30" class="label" text-anchor="middle">Paystack</text>
            <text x="80" y="50" class="desc" text-anchor="middle">Payment Intake API</text>
            <text x="80" y="65" class="desc" text-anchor="middle">Transfers API (Payouts)</text>
            <text x="80" y="80" class="desc" text-anchor="middle">Webhooks</text>
        </g>
         <!-- Posthog -->
        <g transform="translate(200, 20)">
            <rect x="0" y="0" width="160" height="100" rx="10" fill="#fafafa" stroke="#d4d4d4" filter="url(#shadow)" />
            <text x="80" y="50" class="label" text-anchor="middle">PostHog</text>
            <text x="80" y="70" class="desc" text-anchor="middle">(Product Analytics)</text>
        </g>
    </g>

    <!-- Arrows / Connections -->
    <path d="M 200 365 h 50" class="arrow" />
    <text x="225" y="358" class="desc">API Calls</text>
    
    <path d="M 430 365 h 50" class="arrow" />
    <text x="455" y="358" class="desc">DB/Auth</text>

    <path d="M 340 310 v -120 h 140" class="arrow" />
    <text transform="translate(350 240) rotate(-90)" class="desc">Search Query</text>

    <path d="M 680 140 v 20" class="arrow-dashed" />
    <text transform="translate(670 180) rotate(-90)" class="desc">Sync</text>
    
    <path d="M 280 270 v 10" class="arrow-dashed" />
    <path d="M 280 270 h -180 v 250 h 380" class="arrow-dashed" />
    <text x="100" y="510" class="desc" >Webhook</text>
    
    <path d="M 340 420 v 150 h 140" class="arrow" />
    <text x="465" y="500" class="desc">Init Payment</text>
    
     <path d="M 670 500 v 20" class="arrow" />
    <text transform="translate(660 540) rotate(-90)" class="desc">Trigger Payouts</text>
    
    <path d="M 340 420 v 200 h 300" class="arrow" />
    <text x="490" y="610" class="desc">Analytics</text>
</svg>
```

## Questions & Clarifications

  * **Payout Logic:** For automated payouts, what is the business rule? Do we pay owners instantly after a renter's payment clears, or do we hold funds until the booking is complete (to handle potential disputes)? A "payout on completion" model is safer but requires more complex status tracking.
  * **360° Photo Format:** To build the native viewer, we need to enforce a specific image format. Will owners be required to upload a single **equirectangular** panorama image for each virtual tour? This is the standard format for viewers like Pannellum.
  * **Moderation Criteria:** What are the initial high-priority reasons for flagging content (e.g., misleading photos, inappropriate language, suspected scam)? Defining this will help build the "flag" reporting form.
  * **Platform Fees:** How will the platform make money? Will we take a percentage commission from each booking? This is critical for the payout service to calculate the correct amount to transfer to the owner.

## List of Architecture Consideration Questions

  * **Service Communication:** The Deno service now has multiple responsibilities (Sync, Webhooks, Payouts). Should these be three separate, lightweight containers in our Docker setup for better separation of concerns, or is one monolithic Deno service acceptable for the MVP? I lean towards keeping it as one service for MVP simplicity, but we should be aware of the option to split it later.
  * **Image & PDF Security:** How do we handle access control for uploaded legal agreements (PDFs)? They should only be accessible to the owner and the specific renter involved in a confirmed booking. Pocketbase's file access rules will need to be configured carefully for this.
  * **Concurrency on Payouts:** If the app grows, we could have many payouts that need to be processed at once. Does the Deno payout service need a job queue system (like BullMQ with Redis) to manage and retry failed transfers, or can we rely on simple, direct API calls for the MVP? For now, direct calls are fine, but a queue is a natural scaling path.
  * **Admin Tooling:** For the MVP, the admin moderation panel will be basic. What is the most critical information an admin needs to see when reviewing a flagged venue listing to make a decision quickly? (e.g., the listing itself, who flagged it, the reason for flagging, the owner's history).