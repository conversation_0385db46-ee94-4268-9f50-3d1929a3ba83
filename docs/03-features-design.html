<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Design Brief: Venue Rental Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    
    <!-- Chosen Palette: "Venue Verde" - A professional and trustworthy palette using the specified Primary Green, Secondary Yellow, and a range of clean Neutrals to create a high-contrast, accessible, and premium feel. -->
    <!-- Application Structure Plan: A dashboard-style SPA with a persistent sidebar for primary feature navigation ("User Core," "Marketplace," etc.) and a main content area. The content area uses interactive tabs to switch between screens within a feature (e.g., "Login Page," "Admin Dashboard"). This structure allows stakeholders to explore the dense design brief non-linearly, drilling down into specific features of interest without losing context, which is superior to scrolling a long document. A "Feature Flow" chart is included to visually synthesize the user journey. -->
    <!-- Visualization & Content Choices: 
        - Report Info: Feature specifications -> Goal: Organize -> Viz/Presentation: Interactive Sidebar + Tabbed Content -> Interaction: Click to reveal details -> Justification: Breaks down the complex design system into manageable, nested sections, reflecting the information architecture.
        - Report Info: User flow from discovery to booking -> Goal: Change/Process -> Viz/Presentation: Horizontal Process Flow Diagram (HTML/CSS) -> Interaction: Hover to highlight stages -> Justification: Visually communicates the primary user journey in a more engaging way than text alone.
        - Report Info: Screen states and UI/UX details -> Goal: Inform -> Viz/Presentation: Styled text blocks with icons -> Interaction: Static display within tabs -> Justification: Presents the core design specifications in a clean, readable format using typography and color from the style guide.
    -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->

    <style>
        body { font-family: 'Roboto', sans-serif; background-color: #F8F9FA; color: #343A40; }
        h1, h2, h3, h4, h5, h6, .font-poppins { font-family: 'Poppins', sans-serif; }
        .chart-container { position: relative; width: 100%; max-width: 800px; margin-left: auto; margin-right: auto; height: 150px; max-height: 20vh; }
        .sidebar-link { transition: all 0.2s ease-in-out; border-left: 4px solid transparent; }
        .sidebar-link.active { background-color: #E9F7EC; border-left-color: #28A745; color: #218838; font-weight: 600; }
        .sidebar-link:hover:not(.active) { background-color: #F1F3F5; border-left-color: #E9ECEF; }
        .feature-tab { transition: all 0.2s ease-in-out; border-bottom: 2px solid transparent; }
        .feature-tab.active { border-bottom-color: #28A745; color: #28A745; }
        .content-section { display: none; }
        .content-section.active { display: block; }
        .flow-step { transition: all 0.3s ease-in-out; }
        .flow-step:hover { transform: translateY(-4px); border-color: #FFC107; }
        .flow-arrow::after { content: '→'; color: #E9ECEF; font-size: 1.5rem; margin: 0 0.5rem; }
        .flow-step:last-child .flow-arrow::after { content: ''; }
    </style>
</head>
<body class="antialiased">

    <div id="app" class="flex min-h-screen">
        <!-- Sidebar Navigation -->
        <aside id="sidebar" class="w-64 bg-white border-r border-gray-200 p-6 fixed top-0 left-0 h-full overflow-y-auto">
            <h1 class="text-2xl font-bold text-gray-800 font-poppins mb-8">Venue Platform UI</h1>
            <nav id="feature-nav" class="space-y-2">
                <!-- Links will be injected here -->
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="ml-64 flex-1 p-8 lg:p-12">
            <div id="main-content">
                <!-- Content will be injected here -->
            </div>
        </main>
    </div>

    <script>
    const designBrief = {
        features: [
            {
                id: "user_core",
                name: "User, Admin & Profile Core",
                screens: [
                    {
                        name: "Login Page",
                        states: [
                            {
                                name: "Default",
                                description: [
                                    "A two-column layout on desktop, stacking on mobile.",
                                    "**Left Column:** A high-quality image of an elegant event space with a logo and tagline 'Your Space, Your Success.'",
                                    "**Right Column:** A clean form on a `Neutral.lightGray` background. 'Welcome Back' headline. Email/Password fields and a `Primary.green` 'Log In' button.",
                                    "**Animation:** On focus, field borders transition to `Primary.green`. The button has a subtle lift on hover."
                                ]
                            },
                            {
                                name: "Input Validation Error",
                                description: [
                                    "If submission is invalid, the corresponding field border turns red.",
                                    "An inline error message appears below the field (e.g., 'Please enter a valid email address').",
                                    "**Animation:** The 'Log In' button performs a brief horizontal shake to signify the error."
                                ]
                            },
                            {
                                name: "Loading",
                                description: [
                                    "On submission, the button text is replaced by a clean, rotating line-art spinner.",
                                    "**Animation:** Text fades out as the spinner fades in to prevent a jarring swap."
                                ]
                            }
                        ]
                    },
                    {
                        name: "Registration Page",
                        states: [
                            {
                                name: "Default",
                                description: [
                                    "Follows the login page layout with 'Create Your Account' headline.",
                                    "Includes 'Full Name', 'Email', 'Password' fields and a 'Terms of Service' checkbox.",
                                    "**Animation:** The 'Sign Up' button is disabled until all fields are valid and ToS is checked, providing clear affordance."
                                ]
                            }
                        ]
                    },
                    {
                        name: "User Dashboard - Profile",
                        states: [
                            {
                                name: "View Mode",
                                description: [
                                    "Two-column layout with dashboard navigation on the left.",
                                    "Right column shows a 'Profile Information' card with static text and an 'Edit Profile' button.",
                                    "A 'My Roles' section includes a CTA 'Become a Venue Owner' for renters.",
                                    "**Animation:** Content in the right column fades in/out smoothly when navigating sections."
                                ]
                            },
                            {
                                name: "Edit Mode",
                                description: [
                                    "Clicking 'Edit' transforms static text into editable input fields.",
                                    "'Save Changes' and 'Cancel' buttons appear.",
                                    "**Animation:** The 'Save Changes' button has a loading state to provide feedback on API calls."
                                ]
                            }
                        ]
                    },
                    {
                        name: "Admin Dashboard",
                        states: [
                            {
                                name: "Flagged Content",
                                description: [
                                    "A protected, data-dense view for admins.",
                                    "A data table lists flagged content with columns for `Content Type`, `Reporter`, `Reason`, `Status`.",
                                    "Each row has an action menu to 'View Content' or 'Resolve Flag'.",
                                    "**Animation:** Table rows have a subtle hover state to improve usability."
                                ]
                            },
                            {
                                name: "User Management",
                                description: [
                                    "A data table lists all users with columns for `Name`, `Email`, `Roles`, `Status`.",
                                    "A search bar provides live filtering of the user list.",
                                    "**Animation:** The table re-renders smoothly as the admin types in the search bar."
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                id: "marketplace",
                name: "Venue Marketplace",
                screens: [
                    {
                        name: "Venue Search Page",
                        states: [
                            {
                                name: "Initial Load",
                                description: [
                                    "Features a large, inviting search bar with a `Shadows.medium` style.",
                                    "Below the search, a grid of 'Venue Cards' shows featured listings to prevent an empty state.",
                                    "**Animation:** The search bar has a subtle glow on focus. Other content fades out as typing begins."
                                ]
                            },
                            {
                                name: "Active Search",
                                description: [
                                    "As the user types, results instantly populate below the bar in a responsive grid.",
                                    "Each result is a `VenueCard` with an image, title, address, and price.",
                                    "**Animation:** A skeleton loader appears for a split second. Results fade in with a slight stagger."
                                ]
                            }
                        ]
                    },
                    {
                        name: "Single Venue Page",
                        states: [
                            {
                                name: "Default View",
                                description: [
                                    "**Header:** An immersive 360° `PannellumViewer` component.",
                                    "**Body:** A two-column layout on desktop. Left column has venue details, amenities, and photos. Right column has a sticky booking request widget.",
                                    "A small 'Flag' icon is available for reporting.",
                                    "**Animation:** The 360° viewer can parallax scroll to save vertical space."
                                ]
                            },
                            {
                                name: "Flag Modal",
                                description: [
                                    "Clicking 'Flag' opens a modal with a clear title and a textarea for the reason.",
                                    "**Animation:** Modal fades in with a background overlay. A success toast appears on submission."
                                ]
                            }
                        ]
                    },
                    {
                        name: "Create/Edit Venue Form",
                        states: [
                            {
                                name: "Multi-Step Wizard",
                                description: [
                                    "Breaks down the complex form into logical steps: Basics, Details, and Media.",
                                    "A progress bar at the top shows the user's location in the flow.",
                                    "File uploaders show previews and progress bars.",
                                    "**Animation:** Transitions between steps are a smooth horizontal slide."
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                id: "transactions",
                name: "Transaction Lifecycle",
                screens: [
                    {
                        name: "Booking Widget",
                        states: [
                            {
                                name: "Date Selection",
                                description: [
                                    "The widget on the venue page contains date pickers.",
                                    "The calendar view disables already booked dates.",
                                    "A price breakdown appears and updates dynamically.",
                                    "**Animation:** The calendar animates smoothly into view. The price updates instantly."
                                ]
                            }
                        ]
                    },
                    {
                        name: "Bookings Dashboard",
                        states: [
                            {
                                name: "List of Bookings",
                                description: [
                                    "A tabbed interface separates 'Upcoming', 'Past', and 'Pending Requests'.",
                                    "Each booking is a card with venue details and a clear status badge.",
                                    "**Animation:** Status badges use color to communicate state (`yellow` for pending, `green` for paid)."
                                ]
                            },
                            {
                                name: "Booking Details & Chat",
                                description: [
                                    "**Owner View:** Shows 'Approve' and 'Deny' buttons for pending requests.",
                                    "**Renter View:** Shows a 'Pay Now' button for confirmed bookings.",
                                    "**Paid View:** Features a real-time messaging window.",
                                    "**Animation:** New chat messages scroll smoothly into view. The booking status updates in real-time with a color-fade transition."
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                id: "feedback",
                name: "Community & Feedback",
                screens: [
                    {
                        name: "Leave a Review",
                        states: [
                            {
                                name: "Prompt",
                                description: [
                                    "After a booking is completed, an inviting banner appears on the user's dashboard.",
                                    "**Animation:** The banner slides down from the top of the viewport."
                                ]
                            },
                            {
                                name: "Review Form Modal",
                                description: [
                                    "The modal contains an interactive star rating component and a comment textarea.",
                                    "**Animation:** Stars fill with `Secondary.yellow` on hover/click with a subtle 'pop'."
                                ]
                            }
                        ]
                    },
                    {
                        name: "Reviews Section",
                        states: [
                            {
                                name: "Default",
                                description: [
                                    "On the venue page, an aggregate rating is displayed prominently.",
                                    "Below is a paginated list of individual reviews.",
                                    "Owner responses are nested under the corresponding review to create a conversational hierarchy.",
                                    "**Animation:** Individual reviews fade in as the user scrolls down."
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                id: "logistics",
                name: "Engagement & Logistics",
                screens: [
                    {
                        name: "Checklist Creation",
                        states: [
                            {
                                name: "Form",
                                description: [
                                    "In the 'Edit Venue' flow, owners can dynamically add/remove checklist items.",
                                    "**Animation:** A new input field animates smoothly into the list when added."
                                ]
                            }
                        ]
                    },
                    {
                        name: "Renter's Checklist",
                        states: [
                            {
                                name: "To-Do",
                                description: [
                                    "On the booking detail page, the checklist appears for the renter to complete.",
                                    "**Animation:** Checking an item applies a strikethrough and fills the checkbox with `Primary.green`."
                                ]
                            }
                        ]
                    },
                    {
                        name: "Guest Invitations",
                        states: [
                            {
                                name: "Invitation Form",
                                description: [
                                    "After a booking is paid, an 'Invite Guests' button opens a modal.",
                                    "The modal has fields for a custom message and a list of guest emails.",
                                    "**Animation:** A confirmation toast appears on successful submission."
                                ]
                            }
                        ]
                    }
                ]
            }
        ]
    };

    document.addEventListener('DOMContentLoaded', () => {
        const featureNav = document.getElementById('feature-nav');
        const mainContent = document.getElementById('main-content');

        function renderContent(featureId) {
            const feature = designBrief.features.find(f => f.id === featureId);
            if (!feature) return;

            const tabsHtml = feature.screens.map((screen, index) => `
                <button class="feature-tab py-3 px-4 text-sm font-medium text-gray-500 hover:text-gray-700 ${index === 0 ? 'active' : ''}" data-feature-id="${feature.id}" data-screen-index="${index}">
                    ${screen.name}
                </button>
            `).join('');

            const contentHtml = feature.screens.map((screen, index) => `
                <div class="content-section p-1 ${index === 0 ? 'active' : ''}" data-feature-id="${feature.id}" data-screen-index="${index}">
                    ${screen.states.map(state => `
                        <div class="mb-6">
                            <h4 class="text-lg font-semibold text-gray-800 font-poppins">${state.name}</h4>
                            <ul class="mt-2 list-disc list-inside space-y-1 text-gray-600">
                                ${state.description.map(desc => `<li>${desc.replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-gray-700">$1</strong>').replace(/`(.*?)`/g, '<code class="text-sm bg-gray-200 text-gray-800 rounded px-1 py-0.5">$1</code>')}</li>`).join('')}
                            </ul>
                        </div>
                    `).join('')}
                </div>
            `).join('');

            mainContent.innerHTML = `
                <header class="mb-8">
                    <h2 class="text-4xl font-bold text-gray-900 font-poppins">${feature.name}</h2>
                </header>
                <div class="bg-white p-6 lg:p-8 rounded-xl shadow-sm border border-gray-200">
                    <div class="border-b border-gray-200 mb-6">
                        <nav class="-mb-px flex space-x-4 overflow-x-auto" aria-label="Tabs">
                            ${tabsHtml}
                        </nav>
                    </div>
                    <div>
                        ${contentHtml}
                    </div>
                </div>
            `;
            
            attachTabListeners();
        }
        
        function attachTabListeners() {
            document.querySelectorAll('.feature-tab').forEach(tab => {
                tab.addEventListener('click', () => {
                    const featureId = tab.dataset.featureId;
                    const screenIndex = tab.dataset.screenIndex;

                    document.querySelectorAll('.feature-tab').forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');
                    
                    document.querySelectorAll('.content-section').forEach(section => {
                        if(section.dataset.featureId === featureId && section.dataset.screenIndex === screenIndex) {
                            section.classList.add('active');
                        } else {
                            section.classList.remove('active');
                        }
                    });
                });
            });
        }

        designBrief.features.forEach((feature, index) => {
            const link = document.createElement('a');
            link.href = `#${feature.id}`;
            link.textContent = feature.name;
            link.className = `sidebar-link text-gray-600 block p-3 rounded-lg font-medium text-sm ${index === 0 ? 'active' : ''}`;
            link.dataset.featureId = feature.id;
            link.addEventListener('click', (e) => {
                e.preventDefault();
                document.querySelectorAll('.sidebar-link').forEach(l => l.classList.remove('active'));
                link.classList.add('active');
                renderContent(feature.id);
            });
            featureNav.appendChild(link);
        });

        // Initial render
        renderContent(designBrief.features[0].id);
    });
    </script>
</body>
</html>
