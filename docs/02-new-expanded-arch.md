File System
Frontend Repository (astro-venue-frontend)
/src
|-- /assets
|   |-- /emails             // MJML templates for reminders/invites
|   |-- /icons
|   `-- /images
|-- /components
|   |-- /auth
|   |   |-- LoginForm.jsx
|   |   `-- RegisterForm.jsx
|   |-- /common
|   |   |-- Button.jsx
|   |   |-- Modal.jsx
|   |   |-- Spinner.jsx
|   |   `-- StarRating.jsx     // Interactive star rating component
|   |-- /core
|   |   |-- Header.astro
|   |   |-- Footer.astro
|   |   `-- Layout.astro
|   |-- /dashboard
|   |   |-- BookingList.jsx
|   |   |-- ChecklistForm.jsx  // For owners to create checklists
|   |   |-- ChecklistSubmission.jsx // For renters to complete them
|   |   |-- InvitationForm.jsx // For renters to send event invites
|   |   |-- OwnerDashboard.jsx
|   |   |-- ProfileForm.jsx
|   |   `-- ReviewForm.jsx       // For renters to leave a review
|   |-- /admin
|   |   |-- AdminDashboard.jsx
|   |   |-- FlaggedContentTable.jsx
|   |   `-- UserManagementTable.jsx
|   |-- /venues
|   |   |-- BookingForm.jsx
|   |   |-- PannellumViewer.jsx  // Native 360 viewer
|   |   |-- ReviewList.jsx       // Displays reviews on a venue page
|   |   |-- VenueCard.jsx
|   |   |-- VenueForm.jsx
|   |   `-- VenueSearch.jsx
|   `-- /transactions
|       |-- MessagingWindow.jsx
|       `-- PaystackPayment.jsx
|-- /layouts
|   `-- BaseLayout.astro
|-- /lib
|   |-- pocketbase.js
|   |-- meilisearch.js
|   `-- state.js
|-- /pages
|   |-- /auth
|   |   |-- login.astro
|   |   `-- register.astro
|   |-- /dashboard
|   |   |-- index.astro
|   |   `-- profile.astro
|   |-- /admin
|   |   `-- index.astro
|   |-- /venues
|   |   |-- index.astro
|   |   |-- new.astro
|   |   `-- [id].astro
|   |-- /bookings
|   |   |-- index.astro
|   |   `-- [id].astro       // Booking detail, chat, checklist, invite
|   |-- index.astro
|   `-- 404.astro
`-- /styles
    `-- global.css

Backend Repository (deno-venue-services)
/
|-- /src
|   |-- /services
|   |   |-- emailService.ts          // Wrapper for Resend/Postmark
|   |   |-- meilisearchSyncService.ts
|   |   |-- notificationService.ts   // Logic for sending alerts/reminders
|   |   |-- paystackWebhookService.ts
|   |   `-- payoutService.ts
|   |-- /types
|   |   |-- paystack.ts
|   |   `-- pocketbase.ts
|   `-- /utils
|       `-- logger.ts
|-- main.ts
|-- Dockerfile
|-- deps.ts
`-- .env.example

Feature Specifications
Feature 1: User, Admin & Profile Core
Feature Goal: To establish a secure and robust identity system that handles user registration, authentication, role-based access control (RBAC), and content moderation primitives. This is the bedrock of platform trust and safety.

API Relationships: This feature exclusively uses the Pocketbase API for all its operations. The frontend client will interact directly with the Pocketbase instance.

Detailed Feature Requirements:

Users must be able to register with an email and password an oAuth.

Passwords must meet strength requirements (e.g., min 8 chars, 1 uppercase, 1 number).

Upon registration, users are assigned the 'renter' role by default. They can add the 'owner' role from their profile.

Users must log in to access any authenticated parts of the application.

A user with the 'admin' role has access to a separate dashboard.

The admin dashboard must display a list of all users and a list of all flagged content.

Admins must be able to view user details and manually change a user's role or deactivate their account.

Admins must be able to view flagged content details and resolve flags (marking them as 'resolved').

Detailed Implementation Guide:
* **Detailed Implementation Guide:**

    1.  **System Architecture:** A standard client-server model where the Astro/React frontend acts as the client and Pocketbase is the BaaS. The admin dashboard is a set of pages within the Astro application located at `/admin/*`, protected by a layout that verifies the user's role from the global state store.
    2.  **Database Schema Design:**
          * **`users` Collection:**
              * `id`: (PK, default)
              * `email`: (Text, Unique, Required)
              * `name`: (Text, Required)
              * `avatar`: (File)
              * `roles`: (JSON, Required, Default: `["renter"]`). Can contain 'renter', 'owner', 'admin'.
              * `is_active`: (Bool, Default: `true`)
              * `paystack_customer_id`: (Text, optional)
              * `paystack_recipient_code`: (Text, optional, for Owners)
          * **`flagged_content` Collection:**
              * `id`: (PK, default)
              * `content_type`: (Select, Options: 'venue', 'user\_profile', Required)
              * `content_id`: (Text, Required)
              * `reporter`: (Relation to `users`, Required)
              * `reason`: (Text, Required)
              * `status`: (Select, Options: 'open', 'resolved', Default: 'open', Required)
    3.  **Comprehensive API Design (Pocketbase):**
          * `POST /api/collections/users/records`: Create a user. Publicly accessible.
          * `POST /api/collections/users/auth-with-password`: Authenticates a user, returns a JWT.
          * `GET /api/collections/users/records`: List users. API Rule: `@request.auth.roles ~ 'admin'`.
          * `PATCH /api/collections/users/records/:id`: Update a user. API Rule: `@request.auth.id = id || @request.auth.roles ~ 'admin'`.
          * `POST /api/collections/flagged_content/records`: Create a flag. API Rule: `@request.auth.id != ""`.
          * `GET /api/collections/flagged_content/records`: List flags. API Rule: `@request.auth.roles ~ 'admin'`.
    4.  **Frontend Architecture:**
          * State for the authenticated user (including roles) will be managed globally using Nano Stores. On app load, a request is made to refresh the auth state from the Pocketbase cookie.
          * A `AdminLayout.astro` will wrap all pages in `/src/pages/admin/`. This layout will check the global user store; if the user is not present or does not have the 'admin' role, it will redirect to the login page or a 403 page.
    5.  **Detailed CRUD Operations:**
          * **Create:** User registration validates email format and password strength on the client before sending to Pocketbase.
          * **Read:** A user can only read their own full record. An admin can read all records with pagination.
          * **Update:** A user can update their `name` and `avatar`. An admin can update any user's `roles` and `is_active` status.
          * **Delete:** No hard deletes. Admins perform a soft delete by setting `is_active = false`. This preserves user data for historical context (e.g., past bookings).
    6.  **Security Considerations:** JWTs returned by Pocketbase will be stored in a secure, `HttpOnly` cookie. All state-changing forms will implicitly use Pocketbase's anti-CSRF protection. All user-provided text (names, etc.) will be escaped on render to prevent XSS.
    7.  **Testing Strategy:** Unit tests for React form components. Integration tests to verify API rules in Pocketbase (e.g., a non-admin user attempting to list all users should fail). E2E test for the admin login flow and resolving a flagged item.
    8.  **Data Management:** User session is managed via the JWT. Admin table data (users, flags) will use server-side pagination handled by the Pocketbase API.
    9.  **Error Handling & Logging:** Client-side form errors will be displayed next to the relevant fields. API errors (e.g., 401, 403) will be caught by a global fetch wrapper, which will redirect to the login page if necessary. Pocketbase will log all failed API rule validations.

Feature 2: Venue Marketplace (Listing, Touring & Search)
Feature Goal: To provide a fluid and intuitive interface for owners to merchandise their venues effectively, including immersive virtual tours, and for renters to discover them through a powerful, fast search experience.

API Relationships: The frontend interacts with Pocketbase for all CRUD operations on venues and with Meilisearch for all search queries.

Detailed Feature Requirements:

An authenticated owner must be able to access a multi-step "Create Venue" form.

The form must capture title, description, address, capacity, pricing, amenities (as selectable tags), standard photos, one equirectangular 360° photo, and an optional PDF rental agreement.

(REQ010) The listing page must feature a native, interactive 360° photo viewer.

The search interface must provide real-time, typo-tolerant results as the user types.

Any authenticated user must be able to click a "Flag" button on a venue page, which opens a modal to submit a reason for flagging.

Detailed Implementation Guide:
(This section remains unchanged from the previous version, as it correctly incorporates the virtual tour viewer and search functionality.)

Feature 3: Transaction Lifecycle (Booking, Messaging, Payments & Payouts)
Feature Goal: To manage the entire commercial flow in a secure, reliable, and auditable manner, from initial renter inquiry and communication, through booking and payment, to final owner payout.

API Relationships: Frontend <-> Pocketbase (Booking & Chat), Frontend -> Paystack (Payment), Deno Service <-> Paystack (Payouts & Webhooks).

Detailed Feature Requirements:

On a venue page, an authenticated renter can select a start and end date/time and request to book.

This request must trigger a check against existing confirmed bookings for that venue to prevent double-booking.

The property owner receives a notification and can approve or deny the request from their dashboard.

Upon approval, the renter receives a notification and a prompt to pay.

The renter pays the full amount via Paystack.

Upon successful payment, the booking status is updated, and both parties are notified.

After the booking's end date has passed, a payout is automatically scheduled for the owner.

(REQ008) A simple, real-time chat must be available within the context of a single booking, accessible only to the renter and owner.

Detailed Implementation Guide:
(This section remains unchanged from the previous version, as it correctly incorporates the real-time messaging window and transaction flow.)

Feature 4: Community Trust & Feedback (Reviews & Ratings)
Feature Goal: (REQ009) To build a trustworthy marketplace by enabling renters to share their experiences and provide a transparent feedback loop for venue quality and owner conduct.

API Relationships: Frontend <-> Pocketbase. The Deno service may be used to calculate and cache average ratings periodically.

Detailed Feature Requirements:

After a booking's status becomes completed, the renter receives a prompt (via email and in-app notification) to leave a review.

The review form must capture a star rating (1-5) for "Overall Experience" and a free-text comment.

Submitted reviews must be associated with the specific booking, user, and venue.

Property owners must be notified of new reviews and have the ability to write a single, public response.

The average star rating and a list of all reviews (with owner responses nested) must be displayed on the public venue listing page.

Detailed Implementation Guide:

System Architecture: This is a client-to-BaaS feature. The frontend handles form submission, and the backend (Pocketbase) stores the data. A cron job in the Deno service can be added to periodically update a cached average_rating field on the venues collection to optimize frontend load times.

Database Schema Design:

reviews Collection:

id: (PK, default)

booking: (Relation to bookings, Required, Unique - one review per booking)

renter: (Relation to users, Required)

venue: (Relation to venues, Required)

rating: (Number, Required, Min: 1, Max: 5)

comment: (Text, Required)

owner_response: (Text, optional)

Comprehensive API Design (Pocketbase):

POST /api/collections/reviews/records: Create a review. API Rule: @request.auth.id = @request.data.renter && @request.data.booking.status = "completed".

GET /api/collections/reviews/records?filter=(venue.id='VENUE_ID'): List reviews for a venue. Publicly accessible.

PATCH /api/collections/reviews/records/:id: Update a review (for owner response). API Rule: @request.auth.id = owner.id && owner_response = "" (Can only respond once).

Frontend Architecture:

ReviewForm.jsx: A modal component that includes the StarRating.jsx component and a textarea.

ReviewList.jsx: A component on the venue page that fetches and displays all reviews, nesting the owner_response where it exists.

Detailed CRUD Operations:

Create: Renter submits the form. The API rule ensures they can only review a booking they've completed.

Read: Reviews are publicly readable on the venue page, sorted by most recent.

Update: An owner can add an owner_response one time. No other updates are allowed.

Delete: Deletions are admin-only operations for moderation purposes.

Security Considerations: The API rules are critical to prevent review spam or fraudulent reviews. All comments must be sanitized on the frontend before display to prevent XSS.

Testing Strategy: Integration test for the API rule that blocks reviews for incomplete bookings. E2E test for leaving a review and then seeing it appear on the venue page.

Data Management: Average ratings should be calculated and cached on the venues collection to avoid N+1 queries on the search page. Review lists will be paginated.

Error Handling & Logging: If a user tries to review a booking they are not authorized for, the API will return a 403, and the frontend will show a generic error message.

Feature 5: Engagement & Logistics (Alerts, Checklists, Invitations)
Feature Goal: (REQ013, REQ014, REQ015) To automate communication and streamline logistical tasks around a booking, providing value beyond the transaction and improving the user experience for both renters and owners.

API Relationships: Frontend <-> Pocketbase (for data). Frontend -> Deno Service (to trigger emails). Deno Service -> Email Provider (e.g., Resend).

Detailed Feature Requirements:

Alerts: Key events (new booking request, confirmation, payment) must trigger automated emails to the relevant parties.

Checklists: Owners can define a simple move-out checklist template for their venues. Before checkout, renters can access and complete this checklist.

Invitations: After a booking is paid, renters have an option to create a simple event invitation, input guest emails, and have the system send them out.

Detailed Implementation Guide:

System Architecture: Event-driven. Actions in Pocketbase (like a booking status change) will trigger webhooks or be picked up by cron jobs in the Deno service, which then uses an email provider to send notifications.

Database Schema Design:

(Update) venues Collection: Add a field move_out_checklist: (JSON, optional, e.g., [{"item": "Take out trash", "checked": false}, ...]).

checklist_submissions Collection:

id, booking: (Relation, Required, Unique), renter: (Relation, Required)

submission_data: (JSON, Required) // Stores the completed checklist

notes: (Text)

Comprehensive API Design:

The Deno Service will need new internal functions/endpoints triggered by cron jobs or other services.

POST /api/internal/send-invitations: An internal endpoint the frontend could call, or this logic could be in a serverless function. It would take a bookingId and a list of emails.

Frontend Architecture:

ChecklistForm.jsx: A component for owners to create the JSON checklist template in their venue settings.

ChecklistSubmission.jsx: A component for renters, displaying the checklist items as a series of checkboxes.

InvitationForm.jsx: A simple form for the renter to compose a message and list guest emails.

Detailed CRUD Operations:

Checklists: The checklist template is Updated on the venues record by the owner. A checklist_submission is Created once by the renter for a booking.

Invitations: This is an action, not a CRUD operation. It triggers an email-sending job.

Security Considerations: The email invitation endpoint must have strict rate limiting to prevent abuse as an email spamming tool. Checklists submitted by renters must have their text content sanitized.

Testing Strategy: Test the cron job for sending reminders. Test the invitation email formatting and delivery. Test that a renter can successfully fill out and submit a checklist.

Data Management: Checklists are stored as JSON. Email templates can be stored as simple text files in the Deno service repository.

Error Handling & Logging: If the email service provider API is down, the Deno service should log the error and retry sending the notification/invitation later. The user should be informed if their batch of invitations could not be sent.

Feature 6: System Services (Expanded)
Feature Goal: To create a reliable, decoupled backend service that handles all asynchronous tasks, including search syncing, payment processing, and the newly added notification/engagement features.

API Relationships: This service acts as the glue code. Deno <-> Pocketbase, Deno <-> Meilisearch, Paystack <-> Deno, Deno <-> Email Provider.

  * **Detailed Feature Requirements:**

      * **Sync Service:** Any create, update, or delete operation on a published venue in Pocketbase must be reflected in the Meilisearch index within seconds.
      * **Webhook Service:** Must reliably receive and process payment confirmation webhooks from Paystack. Must be resilient to duplicate events.
      * **Payout Service:** Must run on a regular schedule (e.g., daily) to process pending payouts for completed bookings.
      * **Notification Service:** Must run on a schedule (e.g., hourly) to query for upcoming events and send email reminders. Must also be able to send transactional emails on demand (e.g., for invitations).

  * **Detailed Implementation Guide:**

    1.  **System Architecture:** A single Deno application deployed in a Docker container on Fly.io/Railway. The application will run a web server (for the webhook) and a background process (for the sync and scheduled payouts) concurrently.  Within the existing Deno application, add a new notificationService.ts. This service will be called by other services (e.g., after a webhook is processed) and by a new cron job.
    2.  **Implementation - Sync Service:**
          * On startup, the Deno service authenticates with Pocketbase as an admin.
          * It subscribes to the `venues` collection: `pb.collection('venues').subscribe('*', ...)`
          * Inside the callback, it receives an `event` object (`event.action`, `event.record`).
          * `if (event.action === 'create' || event.action === 'update')`: format the `event.record` into the structure Meilisearch expects and call `meiliClient.index('venues').addDocuments([record])`.
          * `if (event.action === 'delete')`: call `meiliClient.index('venues').deleteDocument(event.record.id)`.
          * Include a startup script to perform a full one-time sync of all existing venues to handle initial deployment and disaster recovery.
    3.  **Implementation - Webhook Service:**
          * Use a Deno web framework like Oak. Define the `POST /api/webhooks/paystack` route.
          * **Step 1: Verify Signature.** Create a crypto HMAC-SHA512 hash of the request body using your Paystack secret key. Compare it to the `x-paystack-signature` header. If they don't match, return `400 Bad Request` immediately.
          * **Step 2: Check Idempotency.** When a `charge.success` event comes in, check in the `bookings` collection if a booking with the same `paystack_ref` already has the status `paid`. If so, return `200 OK` to acknowledge the webhook but do nothing further.
          * **Step 3: Process.** If the signature is valid and the event is new, find the booking, update its status to `paid`, and save the record.
    4.  **Implementation - Payout Service:**
          * Use a library like `deno-cron` to schedule a function `processPayouts` to run once daily.
          * The function will:
              * Get the current date. Find all `bookings` where `status = 'paid'` and `end_date` is in the past. Update their status to `completed`.
              * Find all `bookings` where `status = 'completed'` and for which no `payout` record exists.
              * For each of these, calculate the `payout_amount` (`total_price` - `platform_fee`).
              * Call the Paystack Transfers API to send the `payout_amount` to the owner's `paystack_recipient_code`.
              * Create a `payouts` record to log the transaction.
    5.  **Implementation - Notification Service:**
        * Email Provider Integration: Use a library or fetch to interact with an email service like Resend. The API key will be stored as a secure environment variable.

        Cron Job for Reminders:

        cron.schedule('0 * * * *', ...) // Run every hour

        The job queries Pocketbase for bookings where status = 'paid' and start_date is within the next 24-25 hours.

        It checks against a notifications log collection to ensure a reminder hasn't already been sent for this booking.

        If not sent, it calls the email service to send a "Your booking is tomorrow!" email and creates a log entry.

        On-Demand Emails (Invitations):

        Create a function sendInvitationEmail(booking, guestEmails, message).

        This function iterates through the emails, personalizes the MJML template, and calls the email provider API for each one.

        Database Schema Design:

        notifications Collection:

        id, booking: (Relation, Required), user: (Relation, Required)

        type: (Select: 'booking_confirmed', 'payment_reminder', 'event_upcoming', etc.)

        sent_at: (Date, Required)

        Error Handling & Logging: All calls to the email provider API must be wrapped in try...catch. Failures should be logged with the specific error from the provider. For critical notifications, a retry mechanism with exponential backoff should be implemented..
    6.  **Security:** This service is the most sensitive part of the architecture. All secret keys (Pocketbase admin, Meilisearch admin, Paystack secret) **MUST** be injected as environment variables into the container and should never be hardcoded. Network policies on the hosting provider should limit ingress traffic to only what's necessary (e.g., port 443 from Paystack's IP range for the webhook).
    7.  **Testing Strategy:** Pure unit tests for the data transformation logic (PB record -\> MS document). Integration tests for each service function using mocked SDK clients (mock `pb`, `meili`, `paystack`).
    8.  **Data Management:** This service is stateless. Its primary job is to read state from one service and update state in another.
    9.  **Error Handling & Logging:** Wrap all external API calls in `try...catch` blocks. If the Meilisearch sync fails, log the failed record ID and retry with exponential backoff. If a payout fails, update the associated `payouts` record with an 'error' status and reason, and send an alert to an admin channel (e.g., a Slack webhook). Use structured JSON logging for all operations.


