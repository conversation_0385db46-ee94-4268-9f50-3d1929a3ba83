# Feature 1: User, Admin & Profile Core - Testing Guide

This guide provides comprehensive testing instructions for the User, Admin & Profile Core feature implementation.

## Prerequisites

1. **PocketBase Setup**: Ensure PocketBase is running with the collections created
2. **Environment**: Verify all environment variables are configured
3. **Dependencies**: Run `npm install` to ensure all dependencies are installed
4. **Development Server**: Start the development server with `npm run dev`

## Test Categories

### 1. Authentication Flow Testing

#### User Registration
- [ ] **Valid Registration**
  - Navigate to `/auth/register`
  - Fill in valid name, email, password, and confirm password
  - Check password strength indicator updates correctly
  - Agree to terms and conditions
  - Submit form and verify redirect to dashboard
  - Check user is created in PocketBase with default 'renter' role

- [ ] **Password Validation**
  - Test password strength requirements (8+ chars, uppercase, lowercase, number)
  - Verify password confirmation matching
  - Check error messages display correctly

- [ ] **Form Validation**
  - Test empty fields show appropriate errors
  - Test invalid email format
  - Test terms agreement requirement

#### User Login
- [ ] **Valid Login**
  - Navigate to `/auth/login`
  - Enter valid credentials
  - Verify redirect to dashboard
  - Check user state is properly set

- [ ] **Invalid Login**
  - Test incorrect password
  - Test non-existent email
  - Verify error messages display
  - Check shake animation on error

- [ ] **Authenticated User Redirect**
  - Login as user
  - Navigate to `/auth/login` or `/auth/register`
  - Verify redirect to dashboard

### 2. Role-Based Access Control (RBAC)

#### Route Protection
- [ ] **Protected Routes (Unauthenticated)**
  - Try accessing `/dashboard` without login
  - Try accessing `/dashboard/profile` without login
  - Verify redirect to login page with return URL

- [ ] **Admin Routes (Non-Admin User)**
  - Login as regular user (renter/owner)
  - Try accessing `/admin`
  - Verify redirect to dashboard with access denied error

- [ ] **Admin Routes (Admin User)**
  - Login as admin user
  - Access `/admin`, `/admin/users`, `/admin/flagged-content`
  - Verify proper access and functionality

#### Role Management
- [ ] **Default Role Assignment**
  - Register new user
  - Verify default 'renter' role is assigned
  - Check role display in profile

- [ ] **Role Addition**
  - Login as user
  - Navigate to profile
  - Add 'owner' role
  - Verify role is updated in PocketBase and UI

### 3. Profile Management

#### Profile Viewing
- [ ] **Profile Display**
  - Navigate to `/dashboard/profile`
  - Verify user information displays correctly
  - Check avatar placeholder or uploaded image
  - Verify role badges display correctly

#### Profile Editing
- [ ] **Edit Mode**
  - Click "Edit Profile" button
  - Verify form switches to edit mode
  - Test name field editing
  - Test avatar upload functionality

- [ ] **Profile Updates**
  - Update name and save
  - Verify changes persist after page refresh
  - Test cancel functionality

- [ ] **Avatar Upload**
  - Test valid image upload
  - Test file size validation (5MB limit)
  - Test file type validation (images only)

### 4. Admin Dashboard

#### Dashboard Access
- [ ] **Admin Authentication**
  - Login as admin user
  - Navigate to `/admin`
  - Verify dashboard loads with statistics
  - Check navigation between admin sections

#### User Management
- [ ] **User List**
  - Navigate to `/admin/users`
  - Verify user table loads with data
  - Test search functionality
  - Test role and status filters

- [ ] **User Editing**
  - Click edit on a user
  - Test role modification (add/remove roles)
  - Test account activation/deactivation
  - Verify changes save correctly

#### Flagged Content Management
- [ ] **Flagged Content List**
  - Navigate to `/admin/flagged-content`
  - Verify flagged content table loads
  - Test search and filter functionality

- [ ] **Flag Resolution**
  - Test resolving open flags
  - Verify status updates correctly
  - Check resolved flags display properly

### 5. Content Flagging System

#### Flag Submission
- [ ] **Flag Content**
  - Use FlagButton component on content
  - Select reason for flagging
  - Test custom reason input
  - Submit flag and verify success

- [ ] **Flag Validation**
  - Test duplicate flag prevention
  - Test required field validation
  - Test character limits

#### Flag Management
- [ ] **Admin Flag Review**
  - Login as admin
  - View flagged content in admin dashboard
  - Test flag resolution workflow

### 6. UI/UX Testing

#### Design System Compliance
- [ ] **Color Consistency**
  - Verify primary green (#059669) usage
  - Check secondary yellow (#F59E0B) usage
  - Validate neutral color palette

- [ ] **Typography**
  - Check font usage (Poppins/Roboto)
  - Verify heading hierarchy
  - Test text sizing and spacing

#### Responsive Design
- [ ] **Mobile (320px - 768px)**
  - Test authentication pages
  - Test dashboard navigation
  - Test admin tables (horizontal scroll)
  - Test form layouts

- [ ] **Tablet (768px - 1024px)**
  - Test grid layouts
  - Test navigation
  - Test modal dialogs

- [ ] **Desktop (1024px+)**
  - Test two-column auth layouts
  - Test admin dashboard
  - Test table layouts

#### Animations and Interactions
- [ ] **Loading States**
  - Test form submission loading
  - Test table loading states
  - Test dashboard loading

- [ ] **Animations**
  - Test shake animation on form errors
  - Test fade-in animations
  - Test hover effects and transitions

### 7. Error Handling

#### Network Errors
- [ ] **API Failures**
  - Simulate network failures
  - Verify error messages display
  - Test retry functionality

#### Validation Errors
- [ ] **Client-Side Validation**
  - Test form validation messages
  - Test real-time validation feedback

- [ ] **Server-Side Validation**
  - Test PocketBase validation errors
  - Verify error message display

### 8. Security Testing

#### Authentication Security
- [ ] **Session Management**
  - Test session persistence
  - Test logout functionality
  - Test session expiration

#### Authorization Security
- [ ] **Route Protection**
  - Test direct URL access to protected routes
  - Test admin route protection
  - Test API endpoint protection

## Test Data Setup

### Create Test Users
1. **Regular User (Renter)**
   - Email: `<EMAIL>`
   - Password: `TestPass123!`
   - Roles: `["renter"]`

2. **Venue Owner**
   - Email: `<EMAIL>`
   - Password: `TestPass123!`
   - Roles: `["renter", "owner"]`

3. **Admin User**
   - Email: `<EMAIL>`
   - Password: `TestPass123!`
   - Roles: `["admin"]`

### Create Test Flagged Content
1. Create sample flagged content entries for testing admin functionality

## Automated Testing

### Unit Tests
```bash
# Run unit tests for components
npm run test

# Run tests with coverage
npm run test:coverage
```

### Integration Tests
```bash
# Run integration tests
npm run test:integration
```

### E2E Tests
```bash
# Run end-to-end tests
npm run test:e2e
```

## Performance Testing

### Page Load Times
- [ ] Test authentication page load times
- [ ] Test dashboard load times
- [ ] Test admin dashboard performance

### Component Performance
- [ ] Test large table rendering
- [ ] Test form submission performance
- [ ] Test image upload performance

## Browser Compatibility

### Desktop Browsers
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)

### Mobile Browsers
- [ ] Chrome Mobile
- [ ] Safari Mobile
- [ ] Firefox Mobile

## Accessibility Testing

### Keyboard Navigation
- [ ] Test tab navigation through forms
- [ ] Test keyboard shortcuts
- [ ] Test focus indicators

### Screen Reader Compatibility
- [ ] Test with screen reader software
- [ ] Verify ARIA labels
- [ ] Test semantic HTML structure

## Sign-off Checklist

- [ ] All authentication flows working correctly
- [ ] Role-based access control functioning properly
- [ ] Profile management features complete
- [ ] Admin dashboard fully functional
- [ ] Content flagging system operational
- [ ] Design system properly implemented
- [ ] Responsive design working on all devices
- [ ] Loading states and animations functioning
- [ ] Error handling working correctly
- [ ] Security measures in place
- [ ] Performance meets requirements
- [ ] Browser compatibility verified
- [ ] Accessibility standards met

## Known Issues

Document any known issues or limitations here:

1. [Issue description and workaround]
2. [Issue description and workaround]

## Next Steps

After successful testing:

1. Deploy to staging environment
2. Conduct user acceptance testing
3. Prepare for production deployment
4. Monitor system performance
5. Gather user feedback for improvements
