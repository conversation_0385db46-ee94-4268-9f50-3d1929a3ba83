# Trodoo - Premier Venue Rental Platform

A comprehensive, production-ready Astro.js application for venue and property rentals. This platform directly connects renters with property owners, providing an all-in-one solution for venue discovery, booking, and management.

## 🚀 Features

- **Server-Side Rendering (SSR)** with Astro v5.10
- **TypeScript** support with strict configuration
- **Tailwind CSS** for utility-first styling
- **Alpine.js** for lightweight client-side interactivity
- **PocketBase** integration for backend functionality
- **Content Collections** for blog management
- **Middleware** for route protection and security
- **Responsive Design** with modern UI components

## 🛠️ Tech Stack

- **Framework**: Astro v5.10 (SSR mode)
- **Deployment**: Node.js standalone adapter
- **Styling**: Tailwind CSS with custom theme
- **Interactivity**: Alpine.js + React (for Dock component)
- **Backend**: PocketBase
- **Language**: TypeScript
- **Content**: Astro Content Collections
- **UI Components**: Custom Dock component with Framer Motion

## 📁 Project Structure

```
/
├── public/
│   ├── favicon.svg
│   └── robots.txt
├── src/
│   ├── components/
│   │   ├── Header.astro
│   │   └── Footer.astro
│   ├── content/
│   │   ├── config.ts
│   │   └── blog/
│   │       └── first-post.md
│   ├── layouts/
│   │   └── BaseLayout.astro
│   ├── lib/
│   │   └── pocketbase.ts
│   ├── pages/
│   │   ├── index.astro
│   │   └── blog/
│   │       ├── index.astro
│   │       └── [...slug].astro
│   ├── styles/
│   │   └── global.css
│   ├── env.d.ts
│   └── middleware.ts
├── astro.config.mjs
├── package.json
├── tailwind.config.mjs
└── tsconfig.json
```

## 🚀 Getting Started

### Prerequisites

- Node.js (v18, v20, or v22)
- npm, pnpm, or yarn
- PocketBase server (for backend functionality)

### Installation

1. **Install dependencies**:
   ```bash
   npm install --legacy-peer-deps
   ```

   Note: We use `--legacy-peer-deps` to resolve version compatibility between Astro v4.16.18 and the integrations.

2. **Set up environment variables**:
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` and configure your PocketBase URL:
   ```
   POCKETBASE_URL=http://localhost:8090
   PUBLIC_SITE_URL=http://localhost:4321
   ```

3. **Start the development server**:
   ```bash
   npm run dev
   ```

4. **Open your browser** and navigate to `http://localhost:4321`

### PocketBase Setup

1. Download PocketBase from [pocketbase.io](https://pocketbase.io)
2. Start PocketBase server:
   ```bash
   ./pocketbase serve
   ```
3. Access PocketBase admin at `http://localhost:8090/_/`
4. Create your admin account and configure collections as needed

## 🎨 Customization

### Theme Colors

The project uses a custom Tailwind theme with three main color palettes:

- **Primary**: Blue tones for main actions and branding
- **Secondary**: Gray tones for text and backgrounds
- **Accent**: Purple tones for highlights and special elements

Customize colors in `tailwind.config.mjs`.

### Dock Component

The project includes a sophisticated Dock component (from reactbits.dev) with smooth animations and magnification effects. Usage example:

```tsx
import { Dock, type DockItemData } from './components/Dock';
import { VscHome, VscArchive } from 'react-icons/vsc';

const items: DockItemData[] = [
  { icon: <VscHome size={18} />, label: 'Home', onClick: () => alert('Home!') },
  { icon: <VscArchive size={18} />, label: 'Archive', onClick: () => alert('Archive!') },
];

<Dock
  items={items}
  panelHeight={68}
  baseItemSize={50}
  magnification={70}
/>
```

The dock is currently used in the header navigation and provides an interactive, macOS-style dock experience.

### Content Collections

Add new blog posts by creating Markdown files in `src/content/blog/` with the following frontmatter:

```yaml
---
title: 'Your Post Title'
description: 'Post description'
author: 'Author Name'
pubDate: 2025-06-21
heroImage: '/images/hero.jpg' # optional
---
```

## 🔒 Security Features

- Route protection middleware
- Authentication state management
- Security headers (CSP, XSS protection, etc.)
- Protected routes for authenticated users
- Admin-only route protection

## 📝 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run check` - Run Astro type checking

## 🚀 Deployment

The project is configured for Node.js deployment with the standalone adapter. Build the project and deploy the `dist/` folder to your Node.js hosting provider.

```bash
npm run build
```

## 📄 License

This project is part of the Trodoo platform. All rights reserved.

## 🤝 Contributing

This is a demo project for the Trodoo platform. For contributions or questions, please contact the development team.

---

Built with ❤️ using Astro.js
