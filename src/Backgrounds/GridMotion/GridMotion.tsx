import React, { useEffect, useRef, useState } from "react";
import { gsap } from "gsap";

interface GridMotionProps {
  items?: string[];
  gradientColor?: string;
}

const GridMotion: React.FC<GridMotionProps> = ({
  items = [],
  gradientColor = "black",
}) => {
  const gridRef = useRef<HTMLDivElement>(null);
  const rowRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [isMobile, setIsMobile] = useState(false);
  const touchXRef = useRef<number | null>(null);
  const animationRef = useRef<gsap.core.Timeline | null>(null);
  const mouseXRef = useRef(0);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const numRows = 4;
  const numCols = isMobile ? 3 : 7;
  const totalItems = numRows * numCols;

  const defaultItems = Array.from(
    { length: totalItems },
    (_, index) => `Item ${index + 1}`,
  );
  const combinedItems =
    items.length > 0 ? items.slice(0, totalItems) : defaultItems;

  useEffect(() => {
    if (isMobile) {
      const handleTouchStart = (e: TouchEvent) => {
        touchXRef.current = e.touches[0].clientX;
      };

      const handleTouchMove = (e: TouchEvent) => {
        if (touchXRef.current === null) return;
        const deltaX = e.touches[0].clientX - touchXRef.current;
        touchXRef.current = e.touches[0].clientX;
        rowRefs.current.forEach((row) => {
          if (row) {
            gsap.to(row, {
              x: `+=${deltaX}`,
              ease: "power3.out",
              duration: 0.5
            });
          }
        });
      };

      const handleTouchEnd = () => {
        touchXRef.current = null;
      };

      window.addEventListener('touchstart', handleTouchStart);
      window.addEventListener('touchmove', handleTouchMove);
      window.addEventListener('touchend', handleTouchEnd);

      return () => {
        rowRefs.current.forEach(row => {
            if(row) gsap.killTweensOf(row);
        });
        window.removeEventListener('touchstart', handleTouchStart);
        window.removeEventListener('touchmove', handleTouchMove);
        window.removeEventListener('touchend', handleTouchEnd);
      };
    } else {
      gsap.ticker.lagSmoothing(0);

      const handleMouseMove = (e: MouseEvent) => {
        mouseXRef.current = e.clientX;
      };

      const updateMotion = (): void => {
        const mouseX = mouseXRef.current;
        const maxMoveAmount = 300;
        const baseDuration = 0.8;
        const inertiaFactors = [0.6, 0.4, 0.3, 0.2];

        rowRefs.current.forEach((row, index) => {
          if (row) {
            const direction = index % 2 === 0 ? 1 : -1;
            const windowWidth =
              typeof window !== "undefined" ? window.innerWidth : 1200;
            const moveAmount =
              ((mouseX / windowWidth) * maxMoveAmount - maxMoveAmount / 2) *
              direction;

            gsap.to(row, {
              x: moveAmount,
              duration:
                baseDuration + inertiaFactors[index % inertiaFactors.length],
              ease: "power3.out",
              overwrite: "auto",
            });
          }
        });
      };

      if (typeof window !== "undefined") {
        window.addEventListener("mousemove", handleMouseMove);
        gsap.ticker.add(updateMotion);
      }

      return () => {
        if (typeof window !== "undefined") {
          window.removeEventListener("mousemove", handleMouseMove);
          gsap.ticker.remove(updateMotion);
        }
      };
    }
  }, [isMobile]);

  return (
    <div ref={gridRef} className="h-full w-full overflow-hidden">
      <section
        className="w-full h-screen overflow-hidden relative flex items-center justify-center"
        style={{
          background: `radial-gradient(circle, ${gradientColor} 0%, transparent 100%)`,
        }}
      >
        <div className="absolute inset-0 pointer-events-none z-[4] bg-[length:250px]"></div>
        <div
          className="gap-4 flex-none relative w-[150vw] grid grid-cols-1 rotate-[-15deg] origin-center z-[2]"
          style={{
            height: isMobile ? "200vw" : "150vh",
            gridTemplateRows: `repeat(${numRows}, 1fr)`,
          }}
        >
          {Array.from({ length: numRows }, (_, rowIndex) => (
            <div
              key={rowIndex}
              className="grid gap-4"
              style={{
                gridTemplateColumns: `repeat(${
                  isMobile ? numCols * 2 : numCols
                }, 1fr)`,
                willChange: "transform, filter",
              }}
              ref={(el) => {
                if (el) rowRefs.current[rowIndex] = el;
              }}
            >
              {Array.from(
                { length: isMobile ? numCols * 2 : numCols },
                (_, itemIndex) => {
                  const contentIndex = isMobile
                    ? itemIndex % numCols
                    : itemIndex;
                  const content =
                    combinedItems[rowIndex * numCols + contentIndex];
                  return (
                    <div key={itemIndex} className="relative">
                      <div className="relative w-full h-full overflow-hidden rounded-[10px] bg-[#111] flex items-center justify-center text-white text-sm sm:text-lg md:text-xl">
                        {typeof content === "string" &&
                        content.startsWith("http") ? (
                          <div
                            className="w-full h-full bg-cover bg-center absolute top-0 left-0"
                            style={{ backgroundImage: `url(${content})` }}
                          ></div>
                        ) : (
                          <div className="p-2 sm:p-4 text-center z-[1]">
                            {content}
                          </div>
                        )}
                      </div>
                    </div>
                  );
                },
              )}
            </div>
          ))}
        </div>
        <div className="relative w-full h-full top-0 left-0 pointer-events-none"></div>
      </section>
    </div>
  );
};

export default GridMotion;
