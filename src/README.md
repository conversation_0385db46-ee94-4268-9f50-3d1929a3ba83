# Trodoo - Frontend File System Structure

This document outlines the complete file system structure for the Trodoo venue booking platform frontend, built with Astro v5.10 and React.

## Directory Structure Overview

```
/src
├── /assets                 # Static assets and resources
├── /components            # React/Astro components organized by feature
├── /layouts              # Astro layout components
├── /lib                  # Utility libraries and configurations
├── /pages               # Astro pages (file-based routing)
├── /styles             # Global styles and CSS
└── /types             # TypeScript type definitions
```

## Detailed Structure

### `/assets` - Static Assets
```
/assets
├── /emails             # MJML email templates
├── /icons             # SVG icons and icon assets
└── /images           # Static images, logos, placeholders
```

**Purpose**: Contains all static assets used throughout the application.

### `/components` - Component Library
```
/components
├── /auth              # Authentication components
│   ├── LoginForm.tsx
│   └── RegisterForm.tsx
├── /common           # Reusable UI components
│   ├── Button.tsx
│   ├── Modal.tsx
│   ├── Spinner.tsx
│   └── StarRating.tsx
├── /core            # Core layout components
│   ├── Header.astro
│   ├── Footer.astro
│   └── Layout.astro
├── /dashboard       # Dashboard-specific components
│   ├── BookingList.tsx
│   ├── ChecklistForm.tsx
│   ├── ChecklistSubmission.tsx
│   ├── InvitationForm.tsx
│   ├── OwnerDashboard.tsx
│   ├── ProfileForm.tsx
│   └── ReviewForm.tsx
├── /admin          # Admin panel components
│   ├── AdminDashboard.tsx
│   ├── FlaggedContentTable.tsx
│   └── UserManagementTable.tsx
├── /venues        # Venue-related components
│   ├── BookingForm.tsx
│   ├── PannellumViewer.tsx
│   ├── ReviewList.tsx
│   ├── VenueCard.tsx
│   ├── VenueForm.tsx
│   └── VenueSearch.tsx
└── /transactions  # Payment and messaging components
    ├── MessagingWindow.tsx
    └── PaystackPayment.tsx
```

**Purpose**: Organized component library following feature-based architecture.

### `/layouts` - Layout Components
```
/layouts
└── BaseLayout.astro    # Main application layout
```

**Purpose**: Astro layout components for consistent page structure.

### `/lib` - Utility Libraries
```
/lib
├── pocketbase.ts      # PocketBase client and auth utilities
├── meilisearch.js     # MeiliSearch client and search utilities
├── state.js          # Global state management (Nano Stores)
└── utils.ts          # General utility functions
```

**Purpose**: Core utilities, API clients, and state management.

### `/pages` - File-based Routing
```
/pages
├── /auth             # Authentication pages
│   ├── login.astro
│   └── register.astro
├── /dashboard       # User dashboard pages
│   ├── index.astro
│   └── profile.astro
├── /admin          # Admin panel pages
│   └── index.astro
├── /venues        # Venue-related pages
│   ├── index.astro      # Venue listing
│   ├── new.astro        # Create venue
│   └── [id].astro       # Venue detail
├── /bookings     # Booking management pages
│   ├── index.astro      # Booking list
│   └── [id].astro       # Booking detail
├── index.astro   # Home page
└── 404.astro    # Error page
```

**Purpose**: Astro's file-based routing system for all application pages.

### `/types` - TypeScript Definitions
```
/types
├── booking.ts        # Booking-related type definitions
└── venue.ts         # Venue-related type definitions
```

**Purpose**: Centralized TypeScript type definitions for type safety.

## Key Features Implemented

### 1. Authentication System
- **Components**: `LoginForm.tsx`, `RegisterForm.tsx`
- **Pages**: `/auth/login.astro`, `/auth/register.astro`
- **Features**: Form validation, error handling, PocketBase integration

### 2. Dashboard System
- **Components**: `BookingList.tsx`, `OwnerDashboard.tsx`, `ProfileForm.tsx`
- **Pages**: `/dashboard/index.astro`, `/dashboard/profile.astro`
- **Features**: Role-based dashboards, booking management, profile settings

### 3. Venue Marketplace
- **Components**: `VenueCard.tsx`, `VenueSearch.tsx`, `VenueForm.tsx`
- **Pages**: `/venues/index.astro`, `/venues/new.astro`, `/venues/[id].astro`
- **Features**: Venue listing, search, 360° photo support, booking forms

### 4. Booking Management
- **Components**: `BookingForm.tsx`, `ChecklistForm.tsx`, `MessagingWindow.tsx`
- **Pages**: `/bookings/index.astro`, `/bookings/[id].astro`
- **Features**: Booking lifecycle, real-time chat, checklists, invitations

### 5. Admin Panel
- **Components**: `AdminDashboard.tsx`, `FlaggedContentTable.tsx`, `UserManagementTable.tsx`
- **Pages**: `/admin/index.astro`
- **Features**: User management, content moderation, system administration

### 6. Review System
- **Components**: `StarRating.tsx`, `ReviewForm.tsx`, `ReviewList.tsx`
- **Features**: Interactive star ratings, review submission, owner responses

## Technology Stack

- **Framework**: Astro v5.10 with SSR
- **Frontend**: React 18 with TypeScript
- **Styling**: Tailwind CSS with custom design system
- **State Management**: Nano Stores
- **Backend**: PocketBase (BaaS)
- **Search**: MeiliSearch
- **Payments**: Paystack integration
- **360° Photos**: Pannellum viewer

## Design System Integration

All components follow the Trodoo design system defined in `docs/design.json`:
- **Colors**: Primary green (#059669), secondary amber (#F59E0B)
- **Typography**: Poppins/Roboto font stack
- **Spacing**: 8px increment system
- **Components**: Consistent button styles, form elements, and layouts

## Next Steps

1. **Component Implementation**: Complete the remaining placeholder components
2. **API Integration**: Connect components to PocketBase collections
3. **Search Integration**: Implement MeiliSearch for venue discovery
4. **Payment Integration**: Add Paystack payment processing
5. **Testing**: Add unit and integration tests
6. **Deployment**: Configure for production deployment

## Development Guidelines

1. **Component Structure**: Follow the established directory organization
2. **TypeScript**: Use strict typing with defined interfaces
3. **Styling**: Use Tailwind classes with design system tokens
4. **State Management**: Use Nano Stores for global state
5. **Error Handling**: Implement comprehensive error boundaries
6. **Accessibility**: Follow WCAG guidelines for all components

This file system structure provides a solid foundation for building the complete Trodoo platform with clear separation of concerns and scalable architecture.
