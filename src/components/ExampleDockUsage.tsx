import React from 'react';
import { Dock, type DockItemData } from './Dock';
import { 
  VscHome, 
  VscArchive, 
  VscAccount, 
  VscSettingsGear 
} from 'react-icons/vsc';

/**
 * Example component showing how to use the Dock component
 * This matches the sample code provided in the request
 */
const ExampleDockUsage: React.FC = () => {
  const items: DockItemData[] = [
    { icon: <VscHome size={18} />, label: 'Home', onClick: () => alert('Home!') },
    { icon: <VscArchive size={18} />, label: 'Archive', onClick: () => alert('Archive!') },
    { icon: <VscAccount size={18} />, label: 'Profile', onClick: () => alert('Profile!') },
    { icon: <VscSettingsGear size={18} />, label: 'Settings', onClick: () => alert('Settings!') },
  ];

  return (
    <div className="flex justify-center p-8">
      <Dock 
        items={items}
        panelHeight={68}
        baseItemSize={50}
        magnification={70}
      />
    </div>
  );
};

export default ExampleDockUsage;
