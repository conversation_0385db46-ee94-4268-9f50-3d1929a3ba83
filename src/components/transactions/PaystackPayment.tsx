import React, { useState, useEffect } from 'react';
import { CreditCard, Shield, AlertCircle, CheckCircle, ExternalLink } from 'lucide-react';
import type { Booking } from '../../types/booking.ts';
import type { PaymentInitialization } from '../../types/payment.ts';
import Button from '../common/Button.tsx';
import { 
  initializePayment, 
  verifyPayment, 
  generatePaymentReference,
  formatAmount 
} from '../../lib/paystack.ts';
import { updateBookingStatus } from '../../lib/pocketbase.ts';

interface PaystackPaymentProps {
  booking: Booking;
  onSuccess: (reference: string) => void;
  onError: (error: string) => void;
  onClose?: () => void;
  className?: string;
}

interface PaymentState {
  isInitializing: boolean;
  isVerifying: boolean;
  paymentUrl: string | null;
  reference: string | null;
  error: string | null;
  success: boolean;
}

export default function PaystackPayment({
  booking,
  onSuccess,
  onError,
  onClose,
  className = ''
}: PaystackPaymentProps) {
  const [paymentState, setPaymentState] = useState<PaymentState>({
    isInitializing: false,
    isVerifying: false,
    paymentUrl: null,
    reference: null,
    error: null,
    success: false
  });

  // Initialize payment
  const initializePaymentFlow = async () => {
    setPaymentState(prev => ({
      ...prev,
      isInitializing: true,
      error: null
    }));

    try {
      const reference = generatePaymentReference(booking.id);
      const callbackUrl = `${window.location.origin}/bookings/${booking.id}?payment=success`;

      const paymentData: PaymentInitialization = {
        booking_id: booking.id,
        amount: booking.total_price,
        currency: 'NGN',
        customer_email: booking.renter.email,
        reference,
        callback_url: callbackUrl,
        metadata: {
          booking_id: booking.id,
          venue_id: booking.venue.id,
          renter_id: booking.renter.id,
          owner_id: booking.owner.id
        }
      };

      const result = await initializePayment(paymentData);

      if (result.success && result.data) {
        setPaymentState(prev => ({
          ...prev,
          isInitializing: false,
          paymentUrl: result.data!.authorization_url,
          reference
        }));
      } else {
        throw new Error(result.error || 'Failed to initialize payment');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Payment initialization failed';
      setPaymentState(prev => ({
        ...prev,
        isInitializing: false,
        error: errorMessage
      }));
      onError(errorMessage);
    }
  };

  // Verify payment
  const verifyPaymentStatus = async (reference: string) => {
    setPaymentState(prev => ({
      ...prev,
      isVerifying: true,
      error: null
    }));

    try {
      const result = await verifyPayment(reference);

      if (result.success && result.data) {
        if (result.data.status === 'success') {
          // Update booking status in PocketBase
          const updateResult = await updateBookingStatus(booking.id, 'paid', reference);
          
          if (updateResult.success) {
            setPaymentState(prev => ({
              ...prev,
              isVerifying: false,
              success: true
            }));
            onSuccess(reference);
          } else {
            throw new Error('Payment successful but failed to update booking status');
          }
        } else {
          throw new Error(`Payment ${result.data.status}: ${result.data.gateway_response || 'Payment was not successful'}`);
        }
      } else {
        throw new Error(result.error || 'Failed to verify payment');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Payment verification failed';
      setPaymentState(prev => ({
        ...prev,
        isVerifying: false,
        error: errorMessage
      }));
      onError(errorMessage);
    }
  };

  // Handle payment completion (called from popup or redirect)
  const handlePaymentComplete = (reference: string) => {
    if (reference && reference === paymentState.reference) {
      verifyPaymentStatus(reference);
    }
  };

  // Listen for payment completion messages
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.origin !== window.location.origin) return;
      
      if (event.data.type === 'PAYSTACK_PAYMENT_COMPLETE' && event.data.reference) {
        handlePaymentComplete(event.data.reference);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [paymentState.reference]);

  // Open payment in new window
  const openPaymentWindow = () => {
    if (!paymentState.paymentUrl) return;

    const popup = window.open(
      paymentState.paymentUrl,
      'paystack-payment',
      'width=500,height=600,scrollbars=yes,resizable=yes'
    );

    // Poll for popup closure
    const pollTimer = setInterval(() => {
      if (popup?.closed) {
        clearInterval(pollTimer);
        // Check URL parameters for payment status
        const urlParams = new URLSearchParams(window.location.search);
        const reference = urlParams.get('reference');
        if (reference) {
          handlePaymentComplete(reference);
        }
      }
    }, 1000);
  };

  return (
    <div className={`bg-white rounded-xl shadow-card border border-slate-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center mb-6">
        <div className="flex items-center justify-center w-12 h-12 bg-primary-green bg-opacity-10 rounded-lg mr-4">
          <CreditCard className="w-6 h-6 text-primary-green" />
        </div>
        <div>
          <h3 className="text-xl font-semibold text-slate-900">
            Complete Payment
          </h3>
          <p className="text-sm text-slate-600">
            Secure payment powered by Paystack
          </p>
        </div>
      </div>

      {/* Payment Details */}
      <div className="bg-slate-50 rounded-lg p-4 mb-6">
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-sm text-slate-600">Venue:</span>
            <span className="text-sm font-medium text-slate-900">{booking.venue.title}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-slate-600">Booking ID:</span>
            <span className="text-sm font-mono text-slate-900">{booking.id}</span>
          </div>
          <div className="flex justify-between border-t border-slate-200 pt-2">
            <span className="text-base font-medium text-slate-900">Total Amount:</span>
            <span className="text-lg font-bold text-primary-green">
              {formatAmount(booking.total_price)}
            </span>
          </div>
        </div>
      </div>

      {/* Security Notice */}
      <div className="flex items-start bg-blue-50 border border-blue-200 rounded-lg p-3 mb-6">
        <Shield className="w-5 h-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
        <div>
          <p className="text-sm text-blue-800 font-medium">Secure Payment</p>
          <p className="text-xs text-blue-600 mt-1">
            Your payment is processed securely by Paystack. We never store your card details.
          </p>
        </div>
      </div>

      {/* Error Display */}
      {paymentState.error && (
        <div className="flex items-start bg-red-50 border border-red-200 rounded-lg p-3 mb-6">
          <AlertCircle className="w-5 h-5 text-red-600 mr-2 mt-0.5 flex-shrink-0" />
          <div>
            <p className="text-sm text-red-800 font-medium">Payment Error</p>
            <p className="text-xs text-red-600 mt-1">{paymentState.error}</p>
          </div>
        </div>
      )}

      {/* Success Display */}
      {paymentState.success && (
        <div className="flex items-start bg-green-50 border border-green-200 rounded-lg p-3 mb-6">
          <CheckCircle className="w-5 h-5 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
          <div>
            <p className="text-sm text-green-800 font-medium">Payment Successful!</p>
            <p className="text-xs text-green-600 mt-1">
              Your booking has been confirmed. You will receive a confirmation email shortly.
            </p>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="space-y-3">
        {!paymentState.paymentUrl && !paymentState.success ? (
          <Button
            onClick={initializePaymentFlow}
            className="w-full"
            size="lg"
            isLoading={paymentState.isInitializing}
            disabled={paymentState.isInitializing}
          >
            {paymentState.isInitializing ? 'Initializing Payment...' : 'Pay Now'}
          </Button>
        ) : paymentState.paymentUrl && !paymentState.success ? (
          <Button
            onClick={openPaymentWindow}
            className="w-full"
            size="lg"
            isLoading={paymentState.isVerifying}
            disabled={paymentState.isVerifying}
          >
            {paymentState.isVerifying ? (
              'Verifying Payment...'
            ) : (
              <>
                <ExternalLink className="w-4 h-4 mr-2" />
                Open Payment Page
              </>
            )}
          </Button>
        ) : null}

        {onClose && (
          <Button
            onClick={onClose}
            variant="outline"
            className="w-full"
            disabled={paymentState.isInitializing || paymentState.isVerifying}
          >
            {paymentState.success ? 'Close' : 'Cancel'}
          </Button>
        )}
      </div>

      {/* Reference Display */}
      {paymentState.reference && (
        <div className="mt-4 pt-4 border-t border-slate-200">
          <p className="text-xs text-slate-500">
            Payment Reference: <span className="font-mono">{paymentState.reference}</span>
          </p>
        </div>
      )}
    </div>
  );
}
