import { useState, useEffect } from 'react';

interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  roles: string[];
  is_active: boolean;
  created: string;
  updated: string;
  verified: boolean;
}

interface UserManagementTableProps {
  onUserUpdate?: (userId: string, updates: Partial<User>) => void;
  onUserView?: (userId: string) => void;
}

export default function UserManagementTable({ onUserUpdate, onUserView }: UserManagementTableProps) {
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState<'all' | 'renter' | 'owner' | 'admin'>('all');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [updatingUsers, setUpdatingUsers] = useState<Set<string>>(new Set());
  const [editingUser, setEditingUser] = useState<string | null>(null);
  const [editForm, setEditForm] = useState<{ roles: string[]; is_active: boolean }>({
    roles: [],
    is_active: true
  });

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      setIsLoading(true);
      setError('');
      
      // This would normally fetch from PocketBase
      // For now, we'll use mock data
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockUsers: User[] = [
        {
          id: '1',
          name: 'John Doe',
          email: '<EMAIL>',
          avatar: '',
          roles: ['renter'],
          is_active: true,
          created: '2024-01-10T10:30:00Z',
          updated: '2024-01-15T14:20:00Z',
          verified: true
        },
        {
          id: '2',
          name: 'Jane Smith',
          email: '<EMAIL>',
          avatar: '',
          roles: ['renter', 'owner'],
          is_active: true,
          created: '2024-01-08T15:45:00Z',
          updated: '2024-01-14T09:30:00Z',
          verified: true
        },
        {
          id: '3',
          name: 'Mike Johnson',
          email: '<EMAIL>',
          avatar: '',
          roles: ['admin'],
          is_active: true,
          created: '2024-01-01T08:00:00Z',
          updated: '2024-01-15T16:45:00Z',
          verified: true
        },
        {
          id: '4',
          name: 'Sarah Wilson',
          email: '<EMAIL>',
          avatar: '',
          roles: ['renter'],
          is_active: false,
          created: '2024-01-12T12:15:00Z',
          updated: '2024-01-13T10:20:00Z',
          verified: false
        }
      ];
      
      setUsers(mockUsers);
    } catch (err) {
      console.error('Failed to load users:', err);
      setError('Failed to load users');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user.id);
    setEditForm({
      roles: [...user.roles],
      is_active: user.is_active
    });
  };

  const handleSaveEdit = async (userId: string) => {
    try {
      setUpdatingUsers(prev => new Set(prev).add(userId));
      
      // This would normally update in PocketBase
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setUsers(prev => 
        prev.map(user => 
          user.id === userId 
            ? { ...user, ...editForm, updated: new Date().toISOString() }
            : user
        )
      );
      
      setEditingUser(null);
      onUserUpdate?.(userId, editForm);
    } catch (err) {
      console.error('Failed to update user:', err);
      setError('Failed to update user');
    } finally {
      setUpdatingUsers(prev => {
        const newSet = new Set(prev);
        newSet.delete(userId);
        return newSet;
      });
    }
  };

  const handleCancelEdit = () => {
    setEditingUser(null);
    setEditForm({ roles: [], is_active: true });
  };

  const toggleRole = (role: string) => {
    setEditForm(prev => ({
      ...prev,
      roles: prev.roles.includes(role)
        ? prev.roles.filter(r => r !== role)
        : [...prev.roles, role]
    }));
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = searchQuery === '' || 
      user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesRole = roleFilter === 'all' || user.roles.includes(roleFilter);
    const matchesStatus = statusFilter === 'all' || 
      (statusFilter === 'active' && user.is_active) ||
      (statusFilter === 'inactive' && !user.is_active);
    
    return matchesSearch && matchesRole && matchesStatus;
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getRoleBadges = (roles: string[]) => {
    const roleColors = {
      renter: 'bg-blue-100 text-blue-600',
      owner: 'bg-green-100 text-green-600',
      admin: 'bg-red-100 text-red-600'
    };

    return roles.map(role => (
      <span
        key={role}
        className={`px-2 py-1 text-xs rounded-full font-medium ${roleColors[role as keyof typeof roleColors] || 'bg-gray-100 text-gray-600'}`}
      >
        {role.charAt(0).toUpperCase() + role.slice(1)}
      </span>
    ));
  };

  const getStatusBadge = (isActive: boolean, verified: boolean) => {
    if (!isActive) {
      return (
        <span className="px-2 py-1 bg-red-100 text-red-600 text-xs rounded-full font-medium">
          Inactive
        </span>
      );
    }
    if (!verified) {
      return (
        <span className="px-2 py-1 bg-yellow-100 text-yellow-600 text-xs rounded-full font-medium">
          Unverified
        </span>
      );
    }
    return (
      <span className="px-2 py-1 bg-green-100 text-green-600 text-xs rounded-full font-medium">
        Active
      </span>
    );
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-xl shadow-lg border border-[#E5E7EB] p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-[#E5E7EB] rounded w-48"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-[#E5E7EB] rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg border border-[#E5E7EB]">
      {/* Header */}
      <div className="p-6 border-b border-[#E5E7EB]">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div>
            <h3 className="text-lg font-bold text-[#1F2937]">User Management</h3>
            <p className="text-sm text-[#6B7280]">
              Manage user accounts, roles, and permissions
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
            {/* Search */}
            <div className="relative">
              <input
                type="text"
                placeholder="Search users..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full sm:w-64 px-4 py-2 pl-10 border border-[#E5E7EB] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#059669] focus:border-[#059669]"
              />
              <svg className="absolute left-3 top-2.5 h-4 w-4 text-[#6B7280]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            
            {/* Role Filter */}
            <select
              value={roleFilter}
              onChange={(e) => setRoleFilter(e.target.value as any)}
              className="px-4 py-2 border border-[#E5E7EB] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#059669] focus:border-[#059669]"
            >
              <option value="all">All Roles</option>
              <option value="renter">Renter</option>
              <option value="owner">Owner</option>
              <option value="admin">Admin</option>
            </select>
            
            {/* Status Filter */}
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
              className="px-4 py-2 border border-[#E5E7EB] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#059669] focus:border-[#059669]"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="p-4 bg-red-50 border-b border-red-200">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      )}

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-[#F9FAFB] border-b border-[#E5E7EB]">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-[#6B7280] uppercase tracking-wider">
                User
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-[#6B7280] uppercase tracking-wider">
                Roles
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-[#6B7280] uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-[#6B7280] uppercase tracking-wider">
                Joined
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-[#6B7280] uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-[#E5E7EB]">
            {filteredUsers.length === 0 ? (
              <tr>
                <td colSpan={5} className="px-6 py-12 text-center">
                  <div className="text-[#6B7280]">
                    <svg className="mx-auto h-12 w-12 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                    </svg>
                    <p className="text-sm">No users found</p>
                  </div>
                </td>
              </tr>
            ) : (
              filteredUsers.map((user) => (
                <tr key={user.id} className="hover:bg-[#F9FAFB] transition-colors duration-200">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-[#059669] rounded-full flex items-center justify-center">
                        {user.avatar ? (
                          <img src={user.avatar} alt={user.name} className="w-10 h-10 rounded-full object-cover" />
                        ) : (
                          <span className="text-white text-sm font-medium">
                            {user.name.charAt(0).toUpperCase()}
                          </span>
                        )}
                      </div>
                      <div>
                        <div className="text-sm font-medium text-[#1F2937]">{user.name}</div>
                        <div className="text-sm text-[#6B7280]">{user.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {editingUser === user.id ? (
                      <div className="space-y-2">
                        {['renter', 'owner', 'admin'].map(role => (
                          <label key={role} className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              checked={editForm.roles.includes(role)}
                              onChange={() => toggleRole(role)}
                              className="h-4 w-4 text-[#059669] border-[#E5E7EB] rounded focus:ring-[#059669]"
                            />
                            <span className="text-sm text-[#1F2937] capitalize">{role}</span>
                          </label>
                        ))}
                      </div>
                    ) : (
                      <div className="flex flex-wrap gap-1">
                        {getRoleBadges(user.roles)}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {editingUser === user.id ? (
                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={editForm.is_active}
                          onChange={(e) => setEditForm(prev => ({ ...prev, is_active: e.target.checked }))}
                          className="h-4 w-4 text-[#059669] border-[#E5E7EB] rounded focus:ring-[#059669]"
                        />
                        <span className="text-sm text-[#1F2937]">Active</span>
                      </label>
                    ) : (
                      getStatusBadge(user.is_active, user.verified)
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-[#6B7280]">
                    {formatDate(user.created)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    {editingUser === user.id ? (
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={() => handleSaveEdit(user.id)}
                          disabled={updatingUsers.has(user.id)}
                          className="text-green-600 hover:text-green-700 transition-colors duration-300 disabled:opacity-50"
                        >
                          {updatingUsers.has(user.id) ? (
                            <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                          ) : (
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          )}
                        </button>
                        <button
                          onClick={handleCancelEdit}
                          className="text-[#6B7280] hover:text-[#1F2937] transition-colors duration-300"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    ) : (
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={() => onUserView?.(user.id)}
                          className="text-[#059669] hover:text-[#047857] transition-colors duration-300"
                          title="View User"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        </button>
                        <button
                          onClick={() => handleEditUser(user)}
                          className="text-[#6B7280] hover:text-[#1F2937] transition-colors duration-300"
                          title="Edit User"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                      </div>
                    )}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Footer */}
      <div className="px-6 py-4 border-t border-[#E5E7EB] bg-[#F9FAFB]">
        <div className="flex items-center justify-between">
          <p className="text-sm text-[#6B7280]">
            Showing {filteredUsers.length} of {users.length} users
          </p>
          <button
            onClick={loadUsers}
            className="text-sm text-[#059669] hover:text-[#047857] font-medium transition-colors duration-300"
          >
            Refresh
          </button>
        </div>
      </div>
    </div>
  );
}
