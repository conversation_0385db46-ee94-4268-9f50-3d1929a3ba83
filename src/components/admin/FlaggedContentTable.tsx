import { useState, useEffect } from 'react';

interface FlaggedContent {
  id: string;
  content_type: 'venue' | 'user_profile';
  content_id: string;
  reporter: {
    id: string;
    name: string;
    email: string;
  };
  reason: string;
  status: 'open' | 'resolved';
  created: string;
  updated: string;
}

interface FlaggedContentTableProps {
  onResolve?: (id: string) => void;
  onViewContent?: (contentType: string, contentId: string) => void;
}

export default function FlaggedContentTable({ onResolve, onViewContent }: FlaggedContentTableProps) {
  const [flaggedContent, setFlaggedContent] = useState<FlaggedContent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [filter, setFilter] = useState<'all' | 'open' | 'resolved'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [resolvingIds, setResolvingIds] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadFlaggedContent();
  }, []);

  const loadFlaggedContent = async () => {
    try {
      setIsLoading(true);
      setError('');
      
      // This would normally fetch from PocketBase
      // For now, we'll use mock data
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockData: FlaggedContent[] = [
        {
          id: '1',
          content_type: 'venue',
          content_id: 'venue_123',
          reporter: {
            id: 'user_1',
            name: 'John Doe',
            email: '<EMAIL>'
          },
          reason: 'Inappropriate images in venue gallery',
          status: 'open',
          created: '2024-01-15T10:30:00Z',
          updated: '2024-01-15T10:30:00Z'
        },
        {
          id: '2',
          content_type: 'user_profile',
          content_id: 'user_456',
          reporter: {
            id: 'user_2',
            name: 'Jane Smith',
            email: '<EMAIL>'
          },
          reason: 'Fake profile with stolen photos',
          status: 'open',
          created: '2024-01-14T15:20:00Z',
          updated: '2024-01-14T15:20:00Z'
        },
        {
          id: '3',
          content_type: 'venue',
          content_id: 'venue_789',
          reporter: {
            id: 'user_3',
            name: 'Mike Johnson',
            email: '<EMAIL>'
          },
          reason: 'Misleading venue description and pricing',
          status: 'resolved',
          created: '2024-01-13T09:15:00Z',
          updated: '2024-01-14T11:30:00Z'
        }
      ];
      
      setFlaggedContent(mockData);
    } catch (err) {
      console.error('Failed to load flagged content:', err);
      setError('Failed to load flagged content');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResolve = async (id: string) => {
    try {
      setResolvingIds(prev => new Set(prev).add(id));
      
      // This would normally update in PocketBase
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setFlaggedContent(prev => 
        prev.map(item => 
          item.id === id 
            ? { ...item, status: 'resolved' as const, updated: new Date().toISOString() }
            : item
        )
      );
      
      onResolve?.(id);
    } catch (err) {
      console.error('Failed to resolve flag:', err);
      setError('Failed to resolve flag');
    } finally {
      setResolvingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(id);
        return newSet;
      });
    }
  };

  const handleViewContent = (contentType: string, contentId: string) => {
    onViewContent?.(contentType, contentId);
    // This would normally navigate to the content or open a modal
    console.log(`Viewing ${contentType}: ${contentId}`);
  };

  const filteredContent = flaggedContent.filter(item => {
    const matchesFilter = filter === 'all' || item.status === filter;
    const matchesSearch = searchQuery === '' || 
      item.reason.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.reporter.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.reporter.email.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesFilter && matchesSearch;
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status: string) => {
    if (status === 'open') {
      return (
        <span className="px-2 py-1 bg-red-100 text-red-600 text-xs rounded-full font-medium">
          Open
        </span>
      );
    }
    return (
      <span className="px-2 py-1 bg-green-100 text-green-600 text-xs rounded-full font-medium">
        Resolved
      </span>
    );
  };

  const getContentTypeBadge = (contentType: string) => {
    if (contentType === 'venue') {
      return (
        <span className="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded-full font-medium">
          Venue
        </span>
      );
    }
    return (
      <span className="px-2 py-1 bg-purple-100 text-purple-600 text-xs rounded-full font-medium">
        Profile
      </span>
    );
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-xl shadow-lg border border-[#E5E7EB] p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-[#E5E7EB] rounded w-48"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-[#E5E7EB] rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg border border-[#E5E7EB]">
      {/* Header */}
      <div className="p-6 border-b border-[#E5E7EB]">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div>
            <h3 className="text-lg font-bold text-[#1F2937]">Flagged Content</h3>
            <p className="text-sm text-[#6B7280]">
              Review and manage reported content
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
            {/* Search */}
            <div className="relative">
              <input
                type="text"
                placeholder="Search flags..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full sm:w-64 px-4 py-2 pl-10 border border-[#E5E7EB] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#059669] focus:border-[#059669]"
              />
              <svg className="absolute left-3 top-2.5 h-4 w-4 text-[#6B7280]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            
            {/* Filter */}
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value as 'all' | 'open' | 'resolved')}
              className="px-4 py-2 border border-[#E5E7EB] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#059669] focus:border-[#059669]"
            >
              <option value="all">All Status</option>
              <option value="open">Open</option>
              <option value="resolved">Resolved</option>
            </select>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="p-4 bg-red-50 border-b border-red-200">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      )}

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-[#F9FAFB] border-b border-[#E5E7EB]">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-[#6B7280] uppercase tracking-wider">
                Content
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-[#6B7280] uppercase tracking-wider">
                Reporter
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-[#6B7280] uppercase tracking-wider">
                Reason
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-[#6B7280] uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-[#6B7280] uppercase tracking-wider">
                Date
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-[#6B7280] uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-[#E5E7EB]">
            {filteredContent.length === 0 ? (
              <tr>
                <td colSpan={6} className="px-6 py-12 text-center">
                  <div className="text-[#6B7280]">
                    <svg className="mx-auto h-12 w-12 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <p className="text-sm">No flagged content found</p>
                  </div>
                </td>
              </tr>
            ) : (
              filteredContent.map((item) => (
                <tr key={item.id} className="hover:bg-[#F9FAFB] transition-colors duration-200">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-3">
                      {getContentTypeBadge(item.content_type)}
                      <button
                        onClick={() => handleViewContent(item.content_type, item.content_id)}
                        className="text-sm text-[#059669] hover:text-[#047857] font-medium transition-colors duration-300"
                      >
                        {item.content_id}
                      </button>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-[#1F2937]">{item.reporter.name}</div>
                      <div className="text-sm text-[#6B7280]">{item.reporter.email}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-[#1F2937] max-w-xs truncate" title={item.reason}>
                      {item.reason}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(item.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-[#6B7280]">
                    {formatDate(item.created)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        onClick={() => handleViewContent(item.content_type, item.content_id)}
                        className="text-[#059669] hover:text-[#047857] transition-colors duration-300"
                        title="View Content"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      </button>
                      
                      {item.status === 'open' && (
                        <button
                          onClick={() => handleResolve(item.id)}
                          disabled={resolvingIds.has(item.id)}
                          className="text-green-600 hover:text-green-700 transition-colors duration-300 disabled:opacity-50"
                          title="Resolve Flag"
                        >
                          {resolvingIds.has(item.id) ? (
                            <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                          ) : (
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          )}
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Footer */}
      <div className="px-6 py-4 border-t border-[#E5E7EB] bg-[#F9FAFB]">
        <div className="flex items-center justify-between">
          <p className="text-sm text-[#6B7280]">
            Showing {filteredContent.length} of {flaggedContent.length} flags
          </p>
          <button
            onClick={loadFlaggedContent}
            className="text-sm text-[#059669] hover:text-[#047857] font-medium transition-colors duration-300"
          >
            Refresh
          </button>
        </div>
      </div>
    </div>
  );
}
