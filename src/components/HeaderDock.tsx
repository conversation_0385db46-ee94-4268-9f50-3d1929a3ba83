import type React from "react"
import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Home, Search, Plus, Info, LogIn, User } from "lucide-react"
import { useStore } from '@nanostores/react'
import { userStore, initializeAuth } from '../lib/state.ts'

interface DockItem {
  icon: React.ReactNode
  label: string
  href: string
  highlight?: boolean
  badge?: number
}

const HeaderDock: React.FC = () => {
  const user = useStore(userStore)
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)
  const [activeItem, setActiveItem] = useState<string>("home")
  const [footerInView, setFooterInView] = useState(false)
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false)

  // Initialize auth on component mount
  useEffect(() => {
    initializeAuth()
  }, [])

  useEffect(() => {
    const handleScroll = () => {
      const footer = document.getElementById("main-footer")
      if (footer) {
        const rect = footer.getBoundingClientRect()
        const isVisible = rect.top < globalThis.innerHeight && rect.bottom >= 0
        setFooterInView(isVisible)
      }
    }

    globalThis.addEventListener("scroll", handleScroll)
    handleScroll() // Check on initial render

    return () => {
      globalThis.removeEventListener("scroll", handleScroll)
    }
  }, [])

  useEffect(() => {
    const handleFocus = (event: FocusEvent) => {
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        setIsKeyboardVisible(true)
      }
    }

    const handleBlur = () => {
      setIsKeyboardVisible(false)
    }

    globalThis.addEventListener("focusin", handleFocus)
    globalThis.addEventListener("focusout", handleBlur)

    return () => {
      globalThis.removeEventListener("focusin", handleFocus)
      globalThis.removeEventListener("focusout", handleBlur)
    }
  }, [])

  const items: DockItem[] = [
    {
      icon: <Home size={24} />,
      label: "Home",
      href: "/",
    },
    {
      icon: <Search size={24} />,
      label: "Find Venues",
      href: "/venues",
    },
    {
      icon: <Plus size={24} />,
      label: "List Property",
      href: "/venues/new",
      highlight: true,
    },
    {
      icon: <Info size={24} />,
      label: "About",
      href: "/about",
    },
    // Show different item based on authentication status
    user ? {
      icon: <User size={24} />,
      label: "Dashboard",
      href: "/dashboard",
    } : {
      icon: <LogIn size={24} />,
      label: "Sign In",
      href: "/auth/login",
    },
  ]

  const handleItemClick = (href: string, label: string) => {
    setActiveItem(label.toLowerCase().replace(" ", "-"))
    // In a real app, you'd use Next.js router or your routing solution
    globalThis.location.href = href
  }

  return (
    <div
      className={`fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50 font-inter transition-transform duration-300 ${
        isKeyboardVisible ? "translate-y-[200%]" : "translate-y-0"
      }`}
    >
      {/* Main Dock Container */}
      <motion.div
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ type: "spring", stiffness: 300, damping: 30 }}
        className="relative"
      >
        {/* Glassmorphism Background */}
        <div
          className={`absolute inset-0 rounded-2xl border shadow-glassmorphism transition-all duration-300 ${
            footerInView
              ? "bg-slate-800/80 backdrop-blur-xl border-slate-700/50"
              : "bg-white/20 backdrop-blur-2xl border-white/30"
          }`}
        />

        {/* Dock Items Container */}
        <div className="relative flex items-center gap-2 p-4">
          {items.map((item, index) => (
            <DockItem
              key={item.label}
              item={item}
              index={index}
              isHovered={hoveredItem === item.label}
              isActive={activeItem === item.label.toLowerCase().replace(" ", "-")}
              onHover={setHoveredItem}
              onClick={handleItemClick}
            />
          ))}
        </div>

      </motion.div>
    </div>
  )
}

interface DockItemProps {
  item: DockItem
  index: number
  isHovered: boolean
  isActive: boolean
  onHover: (label: string | null) => void
  onClick: (href: string, label: string) => void
}

const DockItem: React.FC<DockItemProps> = ({ item, index, isHovered, isActive, onHover, onClick }) => {
  return (
    <div className="relative">
      <motion.button
        className={`
          relative flex items-center justify-center w-16 h-16 rounded-xl
          transition-all duration-300 ease-out
          ${
            item.highlight
              ? "bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-glow"
              : isActive
                ? "bg-primary-100 text-primary-700"
                : "text-slate-600 hover:bg-slate-100"
          }
          ${!item.highlight && "hover:text-slate-900"}
          focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-20 focus:ring-offset-0
        `}
        whileHover={{
          scale: 1.1,
          y: -2,
        }}
        whileTap={{
          scale: 0.95,
          y: 0,
        }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          delay: index * 0.1,
          type: "spring",
          stiffness: 300,
          damping: 25,
        }}
        onMouseEnter={() => onHover(item.label)}
        onMouseLeave={() => onHover(null)}
        onClick={() => onClick(item.href, item.label)}
      >
        {/* Active Indicator */}
        {isActive && !item.highlight && (
          <motion.div
            layoutId="activeIndicator"
            className="absolute inset-0 bg-primary-100 rounded-xl"
            initial={false}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
          />
        )}

        {/* Icon */}
        <motion.div
          className="relative z-10"
          animate={{
            rotate: isHovered ? [0, -10, 10, 0] : 0,
          }}
          transition={{ duration: 0.3 }}
        >
          {item.icon}
        </motion.div>

        {/* Highlight Glow Effect */}
        {item.highlight && (
          <div className="absolute inset-0 bg-gradient-to-r from-primary-400 to-primary-600 rounded-xl blur-sm opacity-50 -z-10" />
        )}

        {/* Badge */}
        {item.badge && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center"
          >
            {item.badge}
          </motion.div>
        )}
      </motion.button>
      <AnimatePresence>
        {isHovered && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.8 }}
            transition={{ type: "spring", stiffness: 400, damping: 25 }}
            className="absolute -top-16 left-1/2 transform -translate-x-1/2 px-4 py-2 bg-gradient-to-r from-primary-500 to-primary-600 text-white text-base rounded-lg shadow-lg whitespace-nowrap"
          >
            {item.label}
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-primary-600" />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default HeaderDock
