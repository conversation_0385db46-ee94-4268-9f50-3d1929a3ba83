# Dock Component - Design System Implementation

This document outlines how the Dock component implements the Modern Venue Rental Platform design system from `design.json`.

## Color Palette Implementation

### Primary Colors (Green)
- **Primary Green**: `#28A745` - Used for icon colors and hover border states
- **Primary Green Light**: `#34C759` - Available for lighter variations
- **Primary Green Dark**: `#218838` - Available for darker variations

### Secondary Colors (Yellow)
- **Secondary Yellow**: `#FFC107` - Used for hover background states
- **Secondary Yellow Light**: `#FFD03B` - Available for lighter variations
- **Secondary Yellow Dark**: `#E0A800` - Available for darker variations
X
### Neutral Colors
- **White**: `#FFFFFF` - Default background for dock items
- **Light Gray**: `#F8F9FA` - Available for subtle backgrounds
- **Medium Gray**: `#E9ECEF` - Used for borders and default states
- **Dark Gray**: `#6C757D` - Available for secondary text
- **Black**: `#343A40` - Used for tooltip backgrounds

## Typography Implementation

### Font Family
- **Primary**: `'Roboto', 'Helvetica Neue', Arial, sans-serif` - Applied to tooltips and labels
- **Fallback**: `system-ui, -apple-system, sans-serif`

### Font Weights & Sizes
- **Labels**: 14px, weight 500 (medium) - Following design system's small body text specs
- **Regular**: 16px, weight 400 - For general text
- **Medium**: weight 500 - For labels and secondary text

## Spacing Implementation

Following the 8px base unit system:
- **xs**: 4px
- **sm**: 8px  
- **md**: 16px - Used for padding and margins
- **lg**: 24px
- **xl**: 32px - Used for gaps between dock items and container padding
- **2xl**: 48px
- **3xl**: 64px

## Border Radius Implementation

- **sm**: 4px
- **md**: 8px - Used for dock items
- **lg**: 12px
- **xl**: 20px - Used for main dock container
- **full**: 9999px

## Shadow Implementation

- **Subtle**: `0 2px 4px rgba(0, 0, 0, 0.05)`
- **Medium**: `0 4px 8px rgba(0, 0, 0, 0.1)` - Used for tooltips
- **Large**: `0 10px 20px rgba(0, 0, 0, 0.15)` - Used for hover states
- **Card**: `0 8px 16px rgba(0,0,0,0.1)` - Used for dock container and default item state

## Animation Implementation

### Transitions
- **Duration**: 0.3s (300ms) - Following design system specs
- **Easing**: ease-in-out
- **Properties**: color, background-color, transform, box-shadow

### Hover Effects
- **Buttons/Items**: Subtle lift (translateY(-2px)) and background color change
- **Shadow**: Increase from card to large shadow on hover

## Icon Implementation

- **Size**: 24px (regular size from design system)
- **Stroke Width**: 2px (following design system specs)
- **Color**: Primary green (`#28A745`)
- **Style**: Line icons (VSCode icons used)

## Component Structure

### DockItem
- Background: White with medium gray border
- Hover: Yellow background with green border
- Border radius: 8px (md)
- Shadow: Card shadow, increases to large on hover
- Transform: Subtle lift on hover

### DockContainer
- Background: Semi-transparent white (90% opacity)
- Border: Medium gray
- Border radius: 20px (xl)
- Shadow: Card shadow
- Spacing: XL gap between items, XL padding

### DockLabel (Tooltips)
- Background: Black
- Border: Medium gray
- Text: White, 14px, weight 500
- Font: Roboto
- Shadow: Medium shadow
- Border radius: 8px (md)

## Usage Example

```tsx
import { HeaderDock } from '@/components/HeaderDock';

// The component automatically applies all design system styles
<HeaderDock />
```

## Design System Compliance

✅ **Colors**: Fully compliant with green/yellow/neutral palette  
✅ **Typography**: Roboto font family, correct weights and sizes  
✅ **Spacing**: 8px base unit system implemented  
✅ **Border Radius**: md (8px) and xl (20px) values used  
✅ **Shadows**: Card and large shadows implemented  
✅ **Animations**: 0.3s ease-in-out transitions  
✅ **Icons**: 24px size, 2px stroke, green color  
✅ **Hover Effects**: Lift and color changes as specified  

This implementation ensures the Dock component seamlessly integrates with the overall Modern Venue Rental Platform design language.
