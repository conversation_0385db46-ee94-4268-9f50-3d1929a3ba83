import { useState, useEffect } from 'react';
import { useStore } from '@nanostores/react';
import { userStore, authActions } from '../../lib/state.ts';
import type { User } from '../../types/user.ts';

interface ProfileFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export default function ProfileForm({ onSuccess, onCancel }: ProfileFormProps) {
  const user = useStore(userStore);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [formData, setFormData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    avatar: null as File | null,
    avatarPreview: user?.avatar ? `${import.meta.env.POCKETBASE_URL}/api/files/users/${user.id}/${user.avatar}` : ''
  });

  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name || '',
        email: user.email || '',
        avatar: null,
        avatarPreview: user.avatar ? `${import.meta.env.POCKETBASE_URL}/api/files/users/${user.id}/${user.avatar}` : ''
      });
    }
  }, [user]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) setError('');
    if (success) setSuccess('');
  };

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        setError('Please select a valid image file');
        return;
      }
      
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setError('Image must be less than 5MB');
        return;
      }

      setFormData(prev => ({
        ...prev,
        avatar: file,
        avatarPreview: URL.createObjectURL(file)
      }));
      
      if (error) setError('');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      setError('Name is required');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const updateData: Partial<User> = {
        name: formData.name.trim()
      };

      // Handle avatar upload if a new file was selected
      if (formData.avatar) {
        (updateData as any).avatar = formData.avatar;
      }

      const result = await authActions.updateProfile(updateData);
      
      if (result.success) {
        setSuccess('Profile updated successfully!');
        setIsEditing(false);
        onSuccess?.();
        
        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(''), 3000);
      } else {
        setError(result.error || 'Failed to update profile');
      }
    } catch (err) {
      console.error('Profile update error:', err);
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setError('');
    setSuccess('');
    
    // Reset form data to original values
    if (user) {
      setFormData({
        name: user.name || '',
        email: user.email || '',
        avatar: null,
        avatarPreview: user.avatar ? `${import.meta.env.POCKETBASE_URL}/api/files/users/${user.id}/${user.avatar}` : ''
      });
    }
    
    onCancel?.();
  };

  const addRole = async (role: string) => {
    setIsLoading(true);
    try {
      const result = await authActions.addRole(role);
      if (result.success) {
        setSuccess(`${role} role added successfully!`);
        setTimeout(() => setSuccess(''), 3000);
      } else {
        setError(result.error || `Failed to add ${role} role`);
      }
    } catch (_err) {
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="text-center py-8">
        <p className="text-[#6B7280]">Please log in to view your profile.</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg border border-[#E5E7EB] p-8">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-bold text-[#1F2937]">Profile Information</h3>
        {!isEditing && (
          <button
            type="button"
            onClick={() => setIsEditing(true)}
            className="px-4 py-2 border-2 border-[#059669] text-[#059669] rounded-lg font-medium hover:bg-[#059669] hover:text-white transition-all duration-300"
          >
            Edit Profile
          </button>
        )}
      </div>

      {success && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg animate-fade-in">
          <p className="text-sm text-green-600">{success}</p>
        </div>
      )}

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg animate-fade-in">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Avatar Section */}
        <div className="flex items-center space-x-6">
          <div className="relative">
            <div className="w-20 h-20 rounded-full overflow-hidden bg-[#F3F4F6] border-2 border-[#E5E7EB]">
              {formData.avatarPreview ? (
                <img
                  src={formData.avatarPreview}
                  alt="Profile"
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <svg className="w-8 h-8 text-[#9CA3AF]" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                </div>
              )}
            </div>
            {isEditing && (
              <label className="absolute -bottom-2 -right-2 w-8 h-8 bg-[#059669] rounded-full flex items-center justify-center cursor-pointer hover:bg-[#047857] transition-colors duration-300">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleAvatarChange}
                  className="hidden"
                  disabled={isLoading}
                />
              </label>
            )}
          </div>
          
          <div>
            <h4 className="text-lg font-medium text-[#1F2937]">{user.name || 'No name set'}</h4>
            <p className="text-[#6B7280]">{user.email}</p>
            {isEditing && (
              <p className="text-xs text-[#6B7280] mt-1">
                Click the + icon to upload a new profile picture
              </p>
            )}
          </div>
        </div>

        {/* Name Field */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-[#1F2937] mb-2">
            Full Name
          </label>
          {isEditing ? (
            <input
              type="text"
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="w-full px-4 py-3 border border-[#E5E7EB] rounded-lg bg-white text-base transition-all duration-300 ease-in-out focus:outline-none focus:ring-3 focus:ring-[#059669]/20 focus:border-[#059669]"
              placeholder="Enter your full name"
              disabled={isLoading}
            />
          ) : (
            <p className="px-4 py-3 bg-[#F9FAFB] border border-[#E5E7EB] rounded-lg text-[#1F2937]">
              {user.name || 'No name set'}
            </p>
          )}
        </div>

        {/* Email Field (Read-only) */}
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-[#1F2937] mb-2">
            Email Address
          </label>
          <p className="px-4 py-3 bg-[#F9FAFB] border border-[#E5E7EB] rounded-lg text-[#6B7280]">
            {user.email}
          </p>
          <p className="text-xs text-[#6B7280] mt-1">
            Email cannot be changed. Contact support if you need to update your email.
          </p>
        </div>

        {/* Role Management */}
        <div>
          <label className="block text-sm font-medium text-[#1F2937] mb-3">
            My Roles
          </label>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-4 bg-[#F9FAFB] border border-[#E5E7EB] rounded-lg">
              <div>
                <h5 className="font-medium text-[#1F2937]">Renter</h5>
                <p className="text-sm text-[#6B7280]">Book venues for your events</p>
              </div>
              <span className="px-3 py-1 bg-[#059669] text-white text-sm rounded-full">
                Active
              </span>
            </div>
            
            <div className="flex items-center justify-between p-4 bg-[#F9FAFB] border border-[#E5E7EB] rounded-lg">
              <div>
                <h5 className="font-medium text-[#1F2937]">Venue Owner</h5>
                <p className="text-sm text-[#6B7280]">List and manage your venues</p>
              </div>
              {user.roles?.includes('owner') ? (
                <span className="px-3 py-1 bg-[#059669] text-white text-sm rounded-full">
                  Active
                </span>
              ) : (
                <button
                  type="button"
                  onClick={() => addRole('owner')}
                  disabled={isLoading}
                  className="px-4 py-2 bg-[#F59E0B] text-[#1F2937] text-sm font-medium rounded-lg hover:bg-[#D97706] transition-colors duration-300 disabled:opacity-50"
                >
                  Become Owner
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        {isEditing && (
          <div className="flex space-x-4 pt-4">
            <button
              type="submit"
              disabled={isLoading}
              className="flex-1 flex justify-center items-center py-3 px-6 bg-[#F59E0B] text-[#1F2937] font-bold rounded-lg hover:bg-[#D97706] focus:outline-none focus:ring-3 focus:ring-[#F59E0B]/20 disabled:opacity-50 transition-all duration-300"
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </>
              ) : (
                'Save Changes'
              )}
            </button>
            <button
              type="button"
              onClick={handleCancel}
              disabled={isLoading}
              className="flex-1 py-3 px-6 border-2 border-[#E5E7EB] text-[#6B7280] font-medium rounded-lg hover:bg-[#F9FAFB] transition-all duration-300 disabled:opacity-50"
            >
              Cancel
            </button>
          </div>
        )}
      </form>
    </div>
  );
}
