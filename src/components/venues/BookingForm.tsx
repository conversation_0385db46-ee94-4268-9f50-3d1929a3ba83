import React, { useState, useEffect, useCallback } from 'react';
import { Calendar, Clock, Users, DollarSign, AlertCircle, Info } from 'lucide-react';
import type { Venue } from '../../types/venue.ts';
import type { BookingFormData } from '../../types/booking.ts';
import Button from '../common/Button.tsx';
import { checkDateAvailability } from '../../lib/pocketbase.ts';
import { calculateFees } from '../../lib/paystack.ts';

interface BookingFormProps {
  venue: Venue;
  onSubmit: (bookingData: BookingFormData) => Promise<void>;
  className?: string;
  isSticky?: boolean;
}

interface BookingErrors {
  start_date?: string;
  end_date?: string;
  start_time?: string;
  end_time?: string;
  guest_count?: string;
  general?: string;
}

export default function BookingForm({ 
  venue, 
  onSubmit, 
  className = '',
  isSticky = true 
}: BookingFormProps) {
  const [formData, setFormData] = useState({
    start_date: '',
    end_date: '',
    start_time: '09:00',
    end_time: '17:00',
    guest_count: 1,
    message: ''
  });

  const [errors, setErrors] = useState<BookingErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isCheckingAvailability, setIsCheckingAvailability] = useState(false);
  const [totalHours, setTotalHours] = useState(0);
  const [totalAmount, setTotalAmount] = useState(0);
  const [platformFee, setPlatformFee] = useState(0);
  const [payoutAmount, setPayoutAmount] = useState(0);
  const [isAvailable, setIsAvailable] = useState<boolean | null>(null);

  // Calculate total hours and amount when dates/times change
  const calculateTotals = useCallback(() => {
    if (!formData.start_date || !formData.end_date || !formData.start_time || !formData.end_time) {
      setTotalHours(0);
      setTotalAmount(0);
      setPlatformFee(0);
      setPayoutAmount(0);
      return;
    }

    try {
      const startDateTime = new Date(`${formData.start_date}T${formData.start_time}`);
      const endDateTime = new Date(`${formData.end_date}T${formData.end_time}`);

      if (endDateTime <= startDateTime) {
        setTotalHours(0);
        setTotalAmount(0);
        setPlatformFee(0);
        setPayoutAmount(0);
        return;
      }

      const diffMs = endDateTime.getTime() - startDateTime.getTime();
      const hours = diffMs / (1000 * 60 * 60);
      const subtotal = hours * venue.price_per_hour;

      // Calculate fees using Paystack fee calculation
      const feeCalculation = calculateFees(subtotal);

      setTotalHours(Math.round(hours * 100) / 100);
      setTotalAmount(Math.round(feeCalculation.total_amount * 100) / 100);
      setPlatformFee(Math.round(feeCalculation.platform_fee * 100) / 100);
      setPayoutAmount(Math.round(feeCalculation.payout_amount * 100) / 100);
    } catch (_error) {
      setTotalHours(0);
      setTotalAmount(0);
      setPlatformFee(0);
      setPayoutAmount(0);
    }
  }, [formData.start_date, formData.end_date, formData.start_time, formData.end_time, venue.price_per_hour]);

  // Check date availability
  const checkAvailability = useCallback(async () => {
    if (!formData.start_date || !formData.end_date) {
      setIsAvailable(null);
      return;
    }

    setIsCheckingAvailability(true);
    try {
      const startDateTime = `${formData.start_date}T${formData.start_time || '00:00'}:00.000Z`;
      const endDateTime = `${formData.end_date}T${formData.end_time || '23:59'}:00.000Z`;

      const result = await checkDateAvailability(venue.id, startDateTime, endDateTime);
      setIsAvailable(result.available);

      if (!result.available) {
        setErrors(prev => ({
          ...prev,
          general: 'Selected dates are not available. Please choose different dates.'
        }));
      } else {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors.general;
          return newErrors;
        });
      }
    } catch (_error) {
      setIsAvailable(null);
    } finally {
      setIsCheckingAvailability(false);
    }
  }, [formData.start_date, formData.end_date, formData.start_time, formData.end_time, venue.id]);

  useEffect(() => {
    calculateTotals();
  }, [calculateTotals]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      checkAvailability();
    }, 500); // Debounce availability check

    return () => clearTimeout(timeoutId);
  }, [checkAvailability]);

  const updateFormData = (updates: Partial<typeof formData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
    // Clear related errors
    setErrors(prev => {
      const newErrors = { ...prev };
      Object.keys(updates).forEach(key => {
        delete newErrors[key as keyof BookingErrors];
      });
      return newErrors;
    });
  };

  const validateForm = (): boolean => {
    const newErrors: BookingErrors = {};
    const now = new Date();
    const today = now.toISOString().split('T')[0];

    // Date validations
    if (!formData.start_date) {
      newErrors.start_date = 'Start date is required';
    } else if (formData.start_date < today) {
      newErrors.start_date = 'Start date cannot be in the past';
    }

    if (!formData.end_date) {
      newErrors.end_date = 'End date is required';
    } else if (formData.end_date < formData.start_date) {
      newErrors.end_date = 'End date must be after start date';
    }

    // Time validations
    if (!formData.start_time) {
      newErrors.start_time = 'Start time is required';
    }

    if (!formData.end_time) {
      newErrors.end_time = 'End time is required';
    }

    // Check if end time is after start time for same day bookings
    if (formData.start_date === formData.end_date && formData.start_time >= formData.end_time) {
      newErrors.end_time = 'End time must be after start time';
    }

    // Guest count validation
    if (formData.guest_count < 1) {
      newErrors.guest_count = 'At least 1 guest is required';
    } else if (formData.guest_count > venue.capacity) {
      newErrors.guest_count = `Maximum capacity is ${venue.capacity} guests`;
    }

    // Total amount validation
    if (totalAmount <= 0) {
      newErrors.general = 'Please select valid dates and times';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      const startDateTime = `${formData.start_date}T${formData.start_time}:00.000Z`;
      const endDateTime = `${formData.end_date}T${formData.end_time}:00.000Z`;

      const bookingData: BookingFormData = {
        venue_id: venue.id,
        start_date: startDateTime,
        end_date: endDateTime,
        start_time: formData.start_time,
        end_time: formData.end_time,
        guest_count: formData.guest_count,
        total_hours: totalHours,
        total_amount: totalAmount,
        special_requests: formData.message
      };

      await onSubmit(bookingData);
    } catch (_error) {
      setErrors({ general: 'Failed to submit booking request. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const getMinDate = () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow.toISOString().split('T')[0];
  };

  return (
    <div className={`
      bg-white rounded-xl shadow-card border border-slate-200 p-6
      ${isSticky ? 'sticky top-6' : ''}
      ${className}
    `}>
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-slate-900 mb-2">
          Request to Book
        </h3>
        <div className="flex items-center text-lg font-medium text-slate-900">
          <DollarSign className="w-5 h-5 text-primary-green mr-1" />
          {formatPrice(venue.price_per_hour)}/hour
        </div>
      </div>

      {errors.general && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-start">
          <AlertCircle className="w-5 h-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
          <p className="text-sm text-red-600">{errors.general}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Date Selection */}
        <div className="grid grid-cols-2 gap-3">
          <div>
            <label htmlFor="start-date" className="block text-sm font-medium text-slate-700 mb-1">
              Check-in
            </label>
            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
              <input
                type="date"
                id="start-date"
                value={formData.start_date}
                min={getMinDate()}
                onChange={(e) => updateFormData({ start_date: e.target.value })}
                className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-green focus:border-primary-green ${
                  errors.start_date ? 'border-red-300' : 'border-slate-300'
                }`}
              />
            </div>
            {errors.start_date && <p className="mt-1 text-xs text-red-600">{errors.start_date}</p>}
          </div>

          <div>
            <label htmlFor="end-date" className="block text-sm font-medium text-slate-700 mb-1">
              Check-out
            </label>
            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
              <input
                type="date"
                id="end-date"
                value={formData.end_date}
                min={formData.start_date || getMinDate()}
                onChange={(e) => updateFormData({ end_date: e.target.value })}
                className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-green focus:border-primary-green ${
                  errors.end_date ? 'border-red-300' : 'border-slate-300'
                }`}
              />
            </div>
            {errors.end_date && <p className="mt-1 text-xs text-red-600">{errors.end_date}</p>}
          </div>
        </div>

        {/* Time Selection */}
        <div className="grid grid-cols-2 gap-3">
          <div>
            <label htmlFor="start-time" className="block text-sm font-medium text-slate-700 mb-1">
              Start Time
            </label>
            <div className="relative">
              <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
              <input
                type="time"
                id="start-time"
                value={formData.start_time}
                onChange={(e) => updateFormData({ start_time: e.target.value })}
                className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-green focus:border-primary-green ${
                  errors.start_time ? 'border-red-300' : 'border-slate-300'
                }`}
              />
            </div>
            {errors.start_time && <p className="mt-1 text-xs text-red-600">{errors.start_time}</p>}
          </div>

          <div>
            <label htmlFor="end-time" className="block text-sm font-medium text-slate-700 mb-1">
              End Time
            </label>
            <div className="relative">
              <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
              <input
                type="time"
                id="end-time"
                value={formData.end_time}
                onChange={(e) => updateFormData({ end_time: e.target.value })}
                className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-green focus:border-primary-green ${
                  errors.end_time ? 'border-red-300' : 'border-slate-300'
                }`}
              />
            </div>
            {errors.end_time && <p className="mt-1 text-xs text-red-600">{errors.end_time}</p>}
          </div>
        </div>

        {/* Guest Count */}
        <div>
          <label htmlFor="guest-count" className="block text-sm font-medium text-slate-700 mb-1">
            Number of Guests
          </label>
          <div className="relative">
            <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
            <input
              type="number"
              id="guest-count"
              min="1"
              max={venue.capacity}
              value={formData.guest_count}
              onChange={(e) => updateFormData({ guest_count: parseInt(e.target.value) || 1 })}
              className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-green focus:border-primary-green ${
                errors.guest_count ? 'border-red-300' : 'border-slate-300'
              }`}
            />
          </div>
          <p className="mt-1 text-xs text-slate-500">Maximum: {venue.capacity} guests</p>
          {errors.guest_count && <p className="mt-1 text-xs text-red-600">{errors.guest_count}</p>}
        </div>

        {/* Message */}
        <div>
          <label htmlFor="message" className="block text-sm font-medium text-slate-700 mb-1">
            Message to Host (Optional)
          </label>
          <textarea
            id="message"
            rows={3}
            value={formData.message}
            onChange={(e) => updateFormData({ message: e.target.value })}
            placeholder="Tell the host about your event..."
            className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-primary-green focus:border-primary-green resize-none"
          />
        </div>

        {/* Availability Status */}
        {formData.start_date && formData.end_date && (
          <div className={`p-3 rounded-lg border ${
            isAvailable === true
              ? 'bg-green-50 border-green-200'
              : isAvailable === false
                ? 'bg-red-50 border-red-200'
                : 'bg-slate-50 border-slate-200'
          }`}>
            <div className="flex items-center">
              {isCheckingAvailability ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-slate-600 mr-2"></div>
              ) : (
                <div className={`w-2 h-2 rounded-full mr-2 ${
                  isAvailable === true
                    ? 'bg-green-500'
                    : isAvailable === false
                      ? 'bg-red-500'
                      : 'bg-slate-400'
                }`}></div>
              )}
              <span className={`text-sm font-medium ${
                isAvailable === true
                  ? 'text-green-700'
                  : isAvailable === false
                    ? 'text-red-700'
                    : 'text-slate-600'
              }`}>
                {isCheckingAvailability
                  ? 'Checking availability...'
                  : isAvailable === true
                    ? 'Available for selected dates'
                    : isAvailable === false
                      ? 'Not available for selected dates'
                      : 'Select dates to check availability'
                }
              </span>
            </div>
          </div>
        )}

        {/* Pricing Summary */}
        {totalHours > 0 && (
          <div className="border-t border-slate-200 pt-4 space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-slate-600">
                {formatPrice(venue.price_per_hour)} × {totalHours} hour{totalHours !== 1 ? 's' : ''}
              </span>
              <span className="text-slate-900">{formatPrice(totalAmount - platformFee)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-slate-600">Platform fee (5%)</span>
              <span className="text-slate-900">{formatPrice(platformFee)}</span>
            </div>
            <div className="flex justify-between font-semibold text-lg border-t border-slate-200 pt-2">
              <span className="text-slate-900">Total</span>
              <span className="text-primary-green">{formatPrice(totalAmount)}</span>
            </div>
          </div>
        )}

        {/* Submit Button */}
        <Button
          type="submit"
          className="w-full"
          size="lg"
          isLoading={isLoading}
          disabled={totalAmount <= 0 || isAvailable === false || isCheckingAvailability}
        >
          {isCheckingAvailability
            ? 'Checking Availability...'
            : isAvailable === false
              ? 'Dates Not Available'
              : 'Request to Book'
          }
        </Button>

        <p className="text-xs text-slate-500 text-center">
          You won't be charged until your request is approved by the host.
        </p>
      </form>
    </div>
  );
}
