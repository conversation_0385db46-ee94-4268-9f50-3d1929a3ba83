import React, { useState, useCallback } from 'react';
import type { VenueFormData, VenueAddress, VenueValidationErrors } from '../../types/venue.ts';
import Button from '../common/Button.tsx';
import { ChevronLeft, ChevronRight, Upload, X, FileText, Image, Camera, AlertTriangle } from 'lucide-react';
import { validatePanoramaFile, getRecommendedSpecs } from '../../lib/panoramaValidation.ts';

interface VenueFormProps {
  initialData?: Partial<VenueFormData>;
  onSubmit: (data: VenueFormData) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
}

interface FormStep {
  id: number;
  title: string;
  description: string;
}

const FORM_STEPS: FormStep[] = [
  {
    id: 1,
    title: 'The Basics',
    description: 'Tell us about your venue'
  },
  {
    id: 2,
    title: 'Details & Amenities',
    description: 'Capacity, pricing, and features'
  },
  {
    id: 3,
    title: 'Media',
    description: 'Photos and documents'
  }
];

const AMENITY_OPTIONS = [
  'WiFi', 'Parking', 'Kitchen', 'Bar', 'Sound System', 'Projector', 
  'Air Conditioning', 'Heating', 'Wheelchair Access', 'Garden', 
  'Terrace', 'CCTV', 'Security Guard', 'Catering Available'
];

export default function VenueForm({ 
  initialData, 
  onSubmit, 
  onCancel, 
  isLoading = false 
}: VenueFormProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [errors, setErrors] = useState<VenueValidationErrors>({});
  
  // Form data state
  const [formData, setFormData] = useState<VenueFormData>({
    title: initialData?.title || '',
    description: initialData?.description || '',
    address: initialData?.address || {
      street: '',
      city: '',
      country: '',
      state: '',
      postal_code: ''
    },
    capacity: initialData?.capacity || 0,
    price_per_hour: initialData?.price_per_hour || 0,
    amenities: initialData?.amenities || [],
    standard_photos: initialData?.standard_photos || [],
    pano_photo: initialData?.pano_photo || undefined,
    rental_agreement_pdf: initialData?.rental_agreement_pdf || undefined,
    is_published: initialData?.is_published || false
  });

  const updateFormData = useCallback((updates: Partial<VenueFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
    // Clear related errors when user starts typing
    setErrors(prev => {
      const newErrors = { ...prev };
      Object.keys(updates).forEach(key => {
        delete newErrors[key as keyof VenueValidationErrors];
      });
      return newErrors;
    });
  }, []);

  const updateAddress = useCallback((updates: Partial<VenueAddress>) => {
    setFormData(prev => ({
      ...prev,
      address: { ...prev.address, ...updates }
    }));
    setErrors(prev => ({ ...prev, address: undefined }));
  }, []);

  const validateStep = (step: number): boolean => {
    const newErrors: VenueValidationErrors = {};

    if (step === 1) {
      if (!formData.title.trim()) newErrors.title = 'Title is required';
      if (!formData.description.trim()) newErrors.description = 'Description is required';
      if (!formData.address.street.trim()) newErrors.address = 'Street address is required';
      if (!formData.address.city.trim()) newErrors.address = 'City is required';
      if (!formData.address.country.trim()) newErrors.address = 'Country is required';
    }

    if (step === 2) {
      if (formData.capacity <= 0) newErrors.capacity = 'Capacity must be greater than 0';
      if (formData.price_per_hour <= 0) newErrors.price_per_hour = 'Price must be greater than 0';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, FORM_STEPS.length));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    if (validateStep(currentStep)) {
      try {
        await onSubmit(formData);
      } catch (error) {
        setErrors({ general: 'Failed to save venue. Please try again.' });
      }
    }
  };

  const toggleAmenity = (amenity: string) => {
    const newAmenities = formData.amenities.includes(amenity)
      ? formData.amenities.filter(a => a !== amenity)
      : [...formData.amenities, amenity];
    updateFormData({ amenities: newAmenities });
  };

  const handleFileChange = async (field: 'standard_photos' | 'pano_photo' | 'rental_agreement_pdf', files: FileList | null) => {
    if (!files) return;

    if (field === 'standard_photos') {
      const newFiles = Array.from(files);
      updateFormData({ standard_photos: [...formData.standard_photos, ...newFiles] });
    } else if (field === 'pano_photo') {
      const file = files[0];

      // Validate 360° photo
      try {
        const validationResult = await validatePanoramaFile(file);

        if (!validationResult.isValid) {
          setErrors(prev => ({
            ...prev,
            pano_photo: `Invalid 360° photo: ${validationResult.errors.join(', ')}`
          }));
          return;
        }

        if (validationResult.warnings.length > 0) {
          console.warn('360° photo warnings:', validationResult.warnings);
        }

        updateFormData({ [field]: file });
      } catch (error) {
        setErrors(prev => ({
          ...prev,
          pano_photo: 'Failed to validate 360° photo'
        }));
      }
    } else {
      updateFormData({ [field]: files[0] });
    }
  };

  const removePhoto = (index: number) => {
    const newPhotos = formData.standard_photos.filter((_, i) => i !== index);
    updateFormData({ standard_photos: newPhotos });
  };

  const renderProgressBar = () => (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-4">
        {FORM_STEPS.map((step, index) => (
          <div key={step.id} className="flex items-center">
            <div className={`
              w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
              ${currentStep >= step.id 
                ? 'bg-primary-green text-white' 
                : 'bg-slate-200 text-slate-600'
              }
            `}>
              {step.id}
            </div>
            {index < FORM_STEPS.length - 1 && (
              <div className={`
                w-16 h-1 mx-2
                ${currentStep > step.id ? 'bg-primary-green' : 'bg-slate-200'}
              `} />
            )}
          </div>
        ))}
      </div>
      <div className="text-center">
        <h2 className="text-xl font-semibold text-slate-900 mb-1">
          {FORM_STEPS[currentStep - 1].title}
        </h2>
        <p className="text-sm text-slate-600">
          {FORM_STEPS[currentStep - 1].description}
        </p>
      </div>
    </div>
  );

  return (
    <div className="max-w-2xl mx-auto bg-white rounded-xl shadow-card p-8">
      {renderProgressBar()}

      {errors.general && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-600">{errors.general}</p>
        </div>
      )}

      <form onSubmit={(e) => e.preventDefault()}>
        {/* Step 1: The Basics */}
        {currentStep === 1 && (
          <div className="space-y-6">
            <div>
              <label htmlFor="title" className="label">
                Venue Title *
              </label>
              <input
                type="text"
                id="title"
                value={formData.title}
                onChange={(e) => updateFormData({ title: e.target.value })}
                className={`input ${
                  errors.title ? 'input-error' : ''
                }`}
                placeholder="e.g., Beautiful Garden Venue"
              />
              {errors.title && <p className="mt-1 text-sm text-red-600">{errors.title}</p>}
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-slate-900 mb-2">
                Description *
              </label>
              <textarea
                id="description"
                rows={4}
                value={formData.description}
                onChange={(e) => updateFormData({ description: e.target.value })}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-green focus:border-primary-green ${
                  errors.description ? 'border-red-300' : 'border-slate-300'
                }`}
                placeholder="Describe your venue, its atmosphere, and what makes it special..."
              />
              {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="street" className="block text-sm font-medium text-slate-900 mb-2">
                  Street Address *
                </label>
                <input
                  type="text"
                  id="street"
                  value={formData.address.street}
                  onChange={(e) => updateAddress({ street: e.target.value })}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-green focus:border-primary-green ${
                    errors.address ? 'border-red-300' : 'border-slate-300'
                  }`}
                  placeholder="123 Main Street"
                />
              </div>

              <div>
                <label htmlFor="city" className="block text-sm font-medium text-slate-900 mb-2">
                  City *
                </label>
                <input
                  type="text"
                  id="city"
                  value={formData.address.city}
                  onChange={(e) => updateAddress({ city: e.target.value })}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-green focus:border-primary-green ${
                    errors.address ? 'border-red-300' : 'border-slate-300'
                  }`}
                  placeholder="New York"
                />
              </div>

              <div>
                <label htmlFor="state" className="block text-sm font-medium text-slate-900 mb-2">
                  State/Province
                </label>
                <input
                  type="text"
                  id="state"
                  value={formData.address.state || ''}
                  onChange={(e) => updateAddress({ state: e.target.value })}
                  className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-primary-green focus:border-primary-green"
                  placeholder="NY"
                />
              </div>

              <div>
                <label htmlFor="country" className="block text-sm font-medium text-slate-900 mb-2">
                  Country *
                </label>
                <input
                  type="text"
                  id="country"
                  value={formData.address.country}
                  onChange={(e) => updateAddress({ country: e.target.value })}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-green focus:border-primary-green ${
                    errors.address ? 'border-red-300' : 'border-slate-300'
                  }`}
                  placeholder="United States"
                />
              </div>
            </div>

            {errors.address && <p className="text-sm text-red-600">{errors.address}</p>}
          </div>
        )}

        {/* Step 2: Details & Amenities */}
        {currentStep === 2 && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="capacity" className="block text-sm font-medium text-slate-900 mb-2">
                  Maximum Capacity *
                </label>
                <input
                  type="number"
                  id="capacity"
                  min="1"
                  value={formData.capacity || ''}
                  onChange={(e) => updateFormData({ capacity: parseInt(e.target.value) || 0 })}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-green focus:border-primary-green ${
                    errors.capacity ? 'border-red-300' : 'border-slate-300'
                  }`}
                  placeholder="e.g., 100"
                />
                {errors.capacity && <p className="mt-1 text-sm text-red-600">{errors.capacity}</p>}
              </div>

              <div>
                <label htmlFor="price" className="block text-sm font-medium text-slate-900 mb-2">
                  Price per Hour *
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-3 text-slate-500">$</span>
                  <input
                    type="number"
                    id="price"
                    min="0"
                    step="0.01"
                    value={formData.price_per_hour || ''}
                    onChange={(e) => updateFormData({ price_per_hour: parseFloat(e.target.value) || 0 })}
                    className={`w-full pl-8 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-green focus:border-primary-green ${
                      errors.price_per_hour ? 'border-red-300' : 'border-slate-300'
                    }`}
                    placeholder="0.00"
                  />
                </div>
                {errors.price_per_hour && <p className="mt-1 text-sm text-red-600">{errors.price_per_hour}</p>}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-900 mb-3">
                Amenities
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {AMENITY_OPTIONS.map((amenity) => (
                  <label
                    key={amenity}
                    className="flex items-center p-3 border border-slate-200 rounded-lg cursor-pointer hover:bg-slate-50 transition-colors"
                  >
                    <input
                      type="checkbox"
                      checked={formData.amenities.includes(amenity)}
                      onChange={() => toggleAmenity(amenity)}
                      className="w-4 h-4 text-primary-green border-slate-300 rounded focus:ring-primary-green"
                    />
                    <span className="ml-2 text-sm text-slate-700">{amenity}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Step 3: Media */}
        {currentStep === 3 && (
          <div className="space-y-6">
            {/* Standard Photos */}
            <div>
              <label className="block text-sm font-medium text-slate-900 mb-3">
                Venue Photos
              </label>
              <div className="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center hover:border-primary-green transition-colors">
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={(e) => handleFileChange('standard_photos', e.target.files)}
                  className="hidden"
                  id="standard-photos"
                />
                <label htmlFor="standard-photos" className="cursor-pointer">
                  <Image className="w-12 h-12 text-slate-400 mx-auto mb-3" />
                  <p className="text-sm text-slate-600 mb-1">Click to upload venue photos</p>
                  <p className="text-xs text-slate-500">PNG, JPG up to 10MB each</p>
                </label>
              </div>

              {formData.standard_photos.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-4">
                  {formData.standard_photos.map((file, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={URL.createObjectURL(file)}
                        alt={`Preview ${index + 1}`}
                        className="w-full h-24 object-cover rounded-lg"
                      />
                      <button
                        type="button"
                        onClick={() => removePhoto(index)}
                        className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* 360° Photo */}
            <div>
              <label className="block text-sm font-medium text-slate-900 mb-3">
                360° Photo (Optional)
              </label>
              <div className="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center hover:border-primary-green transition-colors">
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => handleFileChange('pano_photo', e.target.files)}
                  className="hidden"
                  id="pano-photo"
                />
                <label htmlFor="pano-photo" className="cursor-pointer">
                  <Camera className="w-12 h-12 text-slate-400 mx-auto mb-3" />
                  <p className="text-sm text-slate-600 mb-1">Upload 360° panoramic photo</p>
                  <p className="text-xs text-slate-500">Equirectangular format, 2:1 aspect ratio</p>
                  <p className="text-xs text-slate-500 mt-1">Recommended: 4096×2048 to 8192×4096 pixels</p>
                </label>
              </div>

              {errors.pano_photo && (
                <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-start">
                    <AlertTriangle className="w-4 h-4 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-sm text-red-600">{errors.pano_photo}</p>
                      <details className="mt-2">
                        <summary className="text-xs text-red-500 cursor-pointer">View requirements</summary>
                        <div className="mt-2 text-xs text-red-600 whitespace-pre-line">
                          {getRecommendedSpecs()}
                        </div>
                      </details>
                    </div>
                  </div>
                </div>
              )}

              {formData.pano_photo && (
                <div className="mt-4 p-3 bg-slate-50 rounded-lg flex items-center justify-between">
                  <span className="text-sm text-slate-700">{formData.pano_photo.name}</span>
                  <button
                    type="button"
                    onClick={() => updateFormData({ pano_photo: undefined })}
                    className="text-red-500 hover:text-red-700"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              )}
            </div>

            {/* Rental Agreement PDF */}
            <div>
              <label className="block text-sm font-medium text-slate-900 mb-3">
                Rental Agreement (Optional)
              </label>
              <div className="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center hover:border-primary-green transition-colors">
                <input
                  type="file"
                  accept=".pdf"
                  onChange={(e) => handleFileChange('rental_agreement_pdf', e.target.files)}
                  className="hidden"
                  id="rental-agreement"
                />
                <label htmlFor="rental-agreement" className="cursor-pointer">
                  <FileText className="w-12 h-12 text-slate-400 mx-auto mb-3" />
                  <p className="text-sm text-slate-600 mb-1">Upload rental agreement</p>
                  <p className="text-xs text-slate-500">PDF format only</p>
                </label>
              </div>

              {formData.rental_agreement_pdf && (
                <div className="mt-4 p-3 bg-slate-50 rounded-lg flex items-center justify-between">
                  <span className="text-sm text-slate-700">{formData.rental_agreement_pdf.name}</span>
                  <button
                    type="button"
                    onClick={() => updateFormData({ rental_agreement_pdf: undefined })}
                    className="text-red-500 hover:text-red-700"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              )}
            </div>

            {/* Publish Option */}
            <div className="border-t border-slate-200 pt-6">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.is_published}
                  onChange={(e) => updateFormData({ is_published: e.target.checked })}
                  className="w-4 h-4 text-primary-green border-slate-300 rounded focus:ring-primary-green"
                />
                <span className="ml-2 text-sm text-slate-700">
                  Publish venue immediately (you can change this later)
                </span>
              </label>
            </div>
          </div>
        )}

        {/* Navigation */}
        <div className="flex justify-between mt-8">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
          >
            Cancel
          </Button>

          <div className="flex gap-3">
            {currentStep > 1 && (
              <Button
                type="button"
                variant="outline"
                onClick={handlePrevious}
                disabled={isLoading}
              >
                <ChevronLeft className="w-4 h-4 mr-1" />
                Previous
              </Button>
            )}

            {currentStep < FORM_STEPS.length ? (
              <Button
                type="button"
                onClick={handleNext}
                disabled={isLoading}
              >
                Next
                <ChevronRight className="w-4 h-4 ml-1" />
              </Button>
            ) : (
              <Button
                type="button"
                onClick={handleSubmit}
                isLoading={isLoading}
              >
                Publish Venue
              </Button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
}
