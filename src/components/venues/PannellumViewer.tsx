import { useEffect, useRef, useState, useCallback } from 'react';
import { AlertCircle, Loader2, RotateCcw, ZoomIn, ZoomOut, Maximize2 } from 'lucide-react';

interface PannellumViewerProps {
  imageUrl: string;
  title?: string;
  className?: string;
  height?: string;
  autoLoad?: boolean;
  showControls?: boolean;
  onLoad?: () => void;
  onError?: (error: string) => void;
}

// Define our own interfaces for Pannellum to avoid namespace issues
interface PannellumConfig {
  type: 'equirectangular';
  panorama: string;
  autoLoad: boolean;
  autoRotate?: number;
  compass?: boolean;
  showControls?: boolean;
  showFullscreenCtrl?: boolean;
  showZoomCtrl?: boolean;
  mouseZoom?: boolean;
  doubleClickZoom?: boolean;
  draggable?: boolean;
  keyboardZoom?: boolean;
  preview?: string;
  loadButtonLabel?: string;
  noPanoramaError?: string;
  fileAccessError?: string;
  malformedURLError?: string;
  iOS?: boolean;
}

interface PannellumViewer {
  destroy: () => void;
  isLoaded: () => boolean;
  getPitch: () => number;
  setPitch: (pitch: number, animated?: boolean) => void;
  getYaw: () => number;
  setYaw: (yaw: number, animated?: boolean) => void;
  getHfov: () => number;
  setHfov: (hfov: number, animated?: boolean) => void;
  startAutoRotate: (speed?: number) => void;
  stopAutoRotate: () => void;
  lookAt: (pitch: number, yaw: number, hfov: number, animated?: boolean) => void;
  resize: () => void;
  on: (event: string, callback: (...args: unknown[]) => void) => void;
  off: (event: string, callback?: (...args: unknown[]) => void) => void;
}



export default function PannellumViewer({
  imageUrl,
  title = '360° View',
  className = '',
  height = '400px',
  autoLoad = true,
  showControls = true,
  onLoad,
  onError
}: PannellumViewerProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const viewerRef = useRef<PannellumViewer | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isScriptLoaded, setIsScriptLoaded] = useState(false);
  const [_isFullscreen, setIsFullscreen] = useState(false);

  // Load Pannellum script dynamically
  const loadPannellumScript = useCallback(() => {
    return new Promise<void>((resolve, reject) => {
      if ((globalThis as unknown as { pannellum?: unknown }).pannellum) {
        resolve();
        return;
      }

      // Load CSS
      const cssLink = document.createElement('link');
      cssLink.rel = 'stylesheet';
      cssLink.href = 'https://cdn.jsdelivr.net/npm/pannellum@2.5.6/build/pannellum.css';
      document.head.appendChild(cssLink);

      // Load JS
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/pannellum@2.5.6/build/pannellum.js';
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load Pannellum script'));
      document.head.appendChild(script);
    });
  }, []);

  // Initialize viewer
  const initializeViewer = useCallback(async () => {
    if (!containerRef.current || !imageUrl) return;

    try {
      setIsLoading(true);
      setError(null);

      if (!isScriptLoaded) {
        await loadPannellumScript();
        setIsScriptLoaded(true);
      }

      // Destroy existing viewer
      if (viewerRef.current) {
        try {
          viewerRef.current.destroy();
        } catch (e) {
          console.warn('Error destroying previous viewer:', e);
        }
      }

      const config: PannellumConfig = {
        type: 'equirectangular',
        panorama: imageUrl,
        autoLoad: autoLoad,
        autoRotate: -2, // Slow auto-rotation
        compass: true,
        showControls: showControls,
        showFullscreenCtrl: true,
        showZoomCtrl: true,
        mouseZoom: true,
        doubleClickZoom: true,
        draggable: true,
        keyboardZoom: true,
        loadButtonLabel: 'Click to Load 360° View',
        noPanoramaError: 'No 360° image was specified.',
        fileAccessError: 'The 360° image could not be accessed.',
        malformedURLError: 'The 360° image URL is malformed.',
        iOS: /iPad|iPhone|iPod/.test(navigator.userAgent)
      };

      // Use the global pannellum object with proper typing
      const pannellumGlobal = (globalThis as unknown as {
        pannellum: {
          viewer: (container: string | HTMLElement, config: PannellumConfig) => PannellumViewer
        }
      });
      viewerRef.current = pannellumGlobal.pannellum.viewer(containerRef.current, config);

      // Set up event listeners
      if (viewerRef.current) {
        viewerRef.current.on('load', () => {
          setIsLoading(false);
          onLoad?.();
        });

        viewerRef.current.on('error', (...args: unknown[]) => {
          const errorMsg = (args[0] as string) || 'Failed to load 360° image';
          setIsLoading(false);
          setError(errorMsg);
          onError?.(errorMsg);
        });

        viewerRef.current.on('errorcleared', () => {
          setError(null);
        });
      }

    } catch (err) {
      setIsLoading(false);
      const errorMessage = err instanceof Error ? err.message : 'Failed to initialize 360° viewer';
      setError(errorMessage);
      onError?.(errorMessage);
    }
  }, [imageUrl, autoLoad, showControls, isScriptLoaded, loadPannellumScript, onLoad, onError]);

  // Control functions
  const resetView = useCallback(() => {
    if (viewerRef.current) {
      viewerRef.current.lookAt(0, 0, 100, true);
    }
  }, []);

  const zoomIn = useCallback(() => {
    if (viewerRef.current) {
      const currentHfov = viewerRef.current.getHfov();
      viewerRef.current.setHfov(Math.max(currentHfov - 10, 50), true);
    }
  }, []);

  const zoomOut = useCallback(() => {
    if (viewerRef.current) {
      const currentHfov = viewerRef.current.getHfov();
      viewerRef.current.setHfov(Math.min(currentHfov + 10, 120), true);
    }
  }, []);

  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      containerRef.current?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  }, []);

  // Initialize viewer when component mounts or imageUrl changes
  useEffect(() => {
    initializeViewer();

    return () => {
      if (viewerRef.current) {
        try {
          viewerRef.current.destroy();
        } catch (e) {
          console.warn('Error destroying viewer on unmount:', e);
        }
      }
    };
  }, [initializeViewer]);

  // Handle fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
      // Resize viewer when entering/exiting fullscreen
      if (viewerRef.current) {
        setTimeout(() => {
          viewerRef.current?.resize();
        }, 100);
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (viewerRef.current) {
        viewerRef.current.resize();
      }
    };

    globalThis.addEventListener('resize', handleResize);
    return () => globalThis.removeEventListener('resize', handleResize);
  }, []);

  if (error) {
    return (
      <div 
        className={`relative bg-slate-100 rounded-lg flex items-center justify-center ${className}`}
        style={{ height }}
      >
        <div className="text-center p-6">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-slate-900 mb-2">
            Failed to load 360° view
          </h3>
          <p className="text-sm text-slate-600 mb-4">{error}</p>
          <button
            type="button"
            onClick={initializeViewer}
            className="px-4 py-2 bg-primary-green text-white rounded-lg hover:bg-primary-greenDark transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative bg-slate-900 rounded-lg overflow-hidden ${className}`} style={{ height }}>
      {/* Viewer Container */}
      <div
        ref={containerRef}
        className="w-full h-full"
        style={{ minHeight: height }}
      />

      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-slate-900 flex items-center justify-center">
          <div className="text-center text-white">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-3" />
            <p className="text-sm">Loading 360° view...</p>
          </div>
        </div>
      )}

      {/* Custom Controls */}
      {showControls && !isLoading && !error && (
        <div className="absolute top-4 right-4 flex flex-col space-y-2">
          <button
            type="button"
            onClick={resetView}
            className="p-2 bg-black/50 text-white rounded-lg hover:bg-black/70 transition-colors"
            title="Reset View"
          >
            <RotateCcw className="w-4 h-4" />
          </button>
          <button
            type="button"
            onClick={zoomIn}
            className="p-2 bg-black/50 text-white rounded-lg hover:bg-black/70 transition-colors"
            title="Zoom In"
          >
            <ZoomIn className="w-4 h-4" />
          </button>
          <button
            type="button"
            onClick={zoomOut}
            className="p-2 bg-black/50 text-white rounded-lg hover:bg-black/70 transition-colors"
            title="Zoom Out"
          >
            <ZoomOut className="w-4 h-4" />
          </button>
          <button
            type="button"
            onClick={toggleFullscreen}
            className="p-2 bg-black/50 text-white rounded-lg hover:bg-black/70 transition-colors"
            title="Fullscreen"
          >
            <Maximize2 className="w-4 h-4" />
          </button>
        </div>
      )}

      {/* Title Overlay */}
      {title && !isLoading && !error && (
        <div className="absolute bottom-4 left-4">
          <div className="bg-black/50 text-white px-3 py-1 rounded-lg text-sm">
            {title}
          </div>
        </div>
      )}
    </div>
  );
}
