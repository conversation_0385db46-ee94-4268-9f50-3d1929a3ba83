import React, { useState, useCallback, useRef } from 'react';
import { FileText, Upload, X, Download, Eye, AlertCircle, CheckCircle } from 'lucide-react';
import { validateFile, formatFileSize, FILE_VALIDATION_PRESETS } from '../../lib/fileUpload.ts';

interface PDFUploadProps {
  file: File | null;
  onFileChange: (file: File | null) => void;
  label?: string;
  description?: string;
  required?: boolean;
  className?: string;
  showPreview?: boolean;
  validationPreset?: keyof typeof FILE_VALIDATION_PRESETS;
}

export default function PDFUpload({
  file,
  onFileChange,
  label = 'Upload PDF',
  description = 'Select a PDF file to upload',
  required = false,
  className = '',
  showPreview = true,
  validationPreset = 'RENTAL_AGREEMENT'
}: PDFUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [showPDFPreview, setShowPDFPreview] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validationOptions = FILE_VALIDATION_PRESETS[validationPreset];

  const handleFileSelect = useCallback((selectedFile: File) => {
    // Validate the file
    const validation = validateFile(selectedFile, validationOptions);

    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }

    setErrors([]);
    onFileChange(selectedFile);
  }, [onFileChange, validationOptions]);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      handleFileSelect(selectedFile);
    }
  }, [handleFileSelect]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const droppedFile = e.dataTransfer.files[0];
    if (droppedFile) {
      handleFileSelect(droppedFile);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const openFileDialog = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const removeFile = useCallback(() => {
    onFileChange(null);
    setErrors([]);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [onFileChange]);

  const downloadFile = useCallback(() => {
    if (!file) return;

    const url = URL.createObjectURL(file);
    const a = document.createElement('a');
    a.href = url;
    a.download = file.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [file]);

  const openPDFPreview = useCallback(() => {
    if (!file) return;
    setShowPDFPreview(true);
  }, [file]);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Label */}
      <label className="block text-sm font-medium text-slate-900">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>

      {/* Upload Area */}
      {!file && (
        <div
          className={`
            border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer
            ${isDragOver 
              ? 'border-primary-green bg-primary-greenSubtle' 
              : 'border-slate-300 hover:border-primary-green'
            }
          `}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={openFileDialog}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept=".pdf,application/pdf"
            onChange={handleFileInputChange}
            className="hidden"
          />

          <FileText className="w-12 h-12 text-slate-400 mx-auto mb-3" />
          <p className="text-sm text-slate-600 mb-1">{description}</p>
          <p className="text-xs text-slate-500 mb-3">
            PDF files only • Max {formatFileSize(validationOptions.maxSize)}
          </p>
          
          <button
            type="button"
            className="px-4 py-2 bg-primary-green text-white rounded-lg hover:bg-primary-greenDark transition-colors"
          >
            <Upload className="w-4 h-4 inline mr-2" />
            Choose PDF File
          </button>
        </div>
      )}

      {/* File Display */}
      {file && (
        <div className="border border-slate-200 rounded-lg p-4 bg-slate-50">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <FileText className="w-6 h-6 text-red-600" />
              </div>
            </div>
            
            <div className="flex-1 min-w-0">
              <h4 className="text-sm font-medium text-slate-900 truncate">
                {file.name}
              </h4>
              <p className="text-sm text-slate-500">
                {formatFileSize(file.size)} • PDF Document
              </p>
              
              {/* Success indicator */}
              <div className="flex items-center mt-2">
                <CheckCircle className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-xs text-green-600">File uploaded successfully</span>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-2">
              {showPreview && (
                <button
                  type="button"
                  onClick={openPDFPreview}
                  className="p-2 text-slate-400 hover:text-slate-600 transition-colors"
                  title="Preview PDF"
                >
                  <Eye className="w-4 h-4" />
                </button>
              )}
              
              <button
                type="button"
                onClick={downloadFile}
                className="p-2 text-slate-400 hover:text-slate-600 transition-colors"
                title="Download PDF"
              >
                <Download className="w-4 h-4" />
              </button>
              
              <button
                type="button"
                onClick={removeFile}
                className="p-2 text-red-400 hover:text-red-600 transition-colors"
                title="Remove PDF"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Error Messages */}
      {errors.length > 0 && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-start">
            <AlertCircle className="w-4 h-4 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-sm font-medium text-red-600 mb-1">Upload errors:</p>
              <ul className="text-sm text-red-600 space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* PDF Preview Modal */}
      {showPDFPreview && file && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl max-h-full w-full h-full flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-slate-200">
              <h3 className="text-lg font-medium text-slate-900 truncate">
                {file.name}
              </h3>
              <div className="flex items-center space-x-2">
                <button
                  onClick={downloadFile}
                  className="p-2 text-slate-400 hover:text-slate-600 transition-colors"
                  title="Download"
                >
                  <Download className="w-5 h-5" />
                </button>
                <button
                  onClick={() => setShowPDFPreview(false)}
                  className="p-2 text-slate-400 hover:text-slate-600 transition-colors"
                  title="Close"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* PDF Viewer */}
            <div className="flex-1 p-4">
              <iframe
                src={URL.createObjectURL(file)}
                className="w-full h-full border border-slate-200 rounded"
                title="PDF Preview"
              />
            </div>
          </div>
        </div>
      )}

      {/* Help Text */}
      {!file && (
        <div className="text-xs text-slate-500">
          <p>Supported format: PDF</p>
          <p>Maximum file size: {formatFileSize(validationOptions.maxSize)}</p>
          {validationPreset === 'RENTAL_AGREEMENT' && (
            <p className="mt-1">Upload your rental agreement template for guests to review and sign.</p>
          )}
        </div>
      )}
    </div>
  );
}
