---
export interface Props {
  title?: string;
  description?: string;
  image?: string;
}

const {
  title = 'Trodoo - Premier Venue Rental Platform',
  description = 'Find and book the perfect venue for your events, meetings, and special occasions. Direct connection between renters and property owners.',
  image = '/images/og-image.jpg'
} = Astro.props;

const canonicalURL = new URL(Astro.url.pathname, Astro.site || 'http://localhost:4321');
---

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={description} />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />

    <!-- Design System Typography -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Anton&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" rel="stylesheet">

    <!-- Canonical URL -->
    <link rel="canonical" href={canonicalURL.toString()} />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content={Astro.url.toString()} />
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:image" content={new URL(image, Astro.site || 'http://localhost:4321').toString()} />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content={Astro.url.toString()} />
    <meta property="twitter:title" content={title} />
    <meta property="twitter:description" content={description} />
    <meta property="twitter:image" content={new URL(image, Astro.site || 'http://localhost:4321').toString()} />

<!-- Alpine.js for interactivity -->
    <script is:inline defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <title>{title}</title>

    <!-- Global Styles -->
    <style>
      @import "/src/styles/global.css";
    </style>

    <!-- Trodoo Vibe Design System CSS Variables -->
    <style>
      :root {
        /* Primary Color Scale - Green */
        --color-primary-50: #ECFDF5;
        --color-primary-100: #D1FAE5;
        --color-primary-200: #A7F3D0;
        --color-primary-300: #6EE7B7;
        --color-primary-400: #34D399;
        --color-primary-500: #10B981;
        --color-primary-600: #059669;
        --color-primary-700: #047857;
        --color-primary-800: #065F46;
        --color-primary-900: #064E3B;

        /* Secondary Color Scale - Amber/Yellow */
        --color-secondary-50: #FFFBEB;
        --color-secondary-100: #FEF3C7;
        --color-secondary-200: #FDE68A;
        --color-secondary-300: #FCD34D;
        --color-secondary-400: #FBBF24;
        --color-secondary-500: #F59E0B;
        --color-secondary-600: #D97706;
        --color-secondary-700: #B45309;
        --color-secondary-800: #92400E;
        --color-secondary-900: #78350F;

        /* Neutral Colors */
        --color-neutral-white: #FFFFFF;
        --color-neutral-light-gray: #F9FAFB;
        --color-neutral-medium-gray: #E5E7EB;
        --color-neutral-dark-gray: #6B7280;
        --color-neutral-charcoal: #374151;
        --color-neutral-black: #1F2937;

        /* Slate Scale */
        --color-slate-50: #F8FAFC;
        --color-slate-100: #F1F5F9;
        --color-slate-200: #E2E8F0;
        --color-slate-300: #CBD5E1;
        --color-slate-400: #94A3B8;
        --color-slate-500: #64748B;
        --color-slate-600: #475569;
        --color-slate-700: #334155;
        --color-slate-800: #1E293B;
        --color-slate-900: #0F172A;

        /* Typography */
        --font-primary: 'Poppins', 'Helvetica Neue', Arial, sans-serif;
        --font-body: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
        --font-display: 'Anton', sans-serif;
        --font-interface: 'Inter', sans-serif;

        /* Spacing (rem-based) */
        --spacing-xs: 0.25rem;
        --spacing-sm: 0.5rem;
        --spacing-md: 1rem;
        --spacing-lg: 1.5rem;
        --spacing-xl: 2rem;
        --spacing-2xl: 3rem;
        --spacing-3xl: 4rem;
        --spacing-4xl: 5rem;
        --spacing-5xl: 6rem;

        /* Border Radius */
        --radius-sm: 0.25rem;
        --radius-md: 0.5rem;
        --radius-lg: 0.75rem;
        --radius-xl: 1.25rem;
        --radius-2xl: 1rem;
        --radius-full: 9999px;

        /* Shadows */
        --shadow-subtle: 0 2px 4px rgba(0, 0, 0, 0.05);
        --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.1);
        --shadow-large: 0 10px 20px rgba(0, 0, 0, 0.15);
        --shadow-card: 0 8px 16px rgba(0,0,0,0.1);
        --shadow-glassmorphism: 0 8px 32px rgba(0, 0, 0, 0.1);
        --shadow-glow: 0 0 20px rgba(5, 150, 105, 0.3);

        /* Gradients */
        --gradient-primary: linear-gradient(135deg, #059669 0%, #10B981 100%);
        --gradient-secondary: linear-gradient(135deg, #F59E0B 0%, #FBBF24 100%);
        --gradient-hero: linear-gradient(135deg, rgba(5, 150, 105, 0.9) 0%, rgba(16, 185, 129, 0.8) 100%);
        --gradient-glassmorphism: rgba(255, 255, 255, 0.2);
        --gradient-glassmorphism-dark: rgba(0, 0, 0, 0.1);
      }

      body {
        font-family: var(--font-body);
        color: var(--color-slate-900);
        background-color: var(--color-neutral-white);
        line-height: 1.5;
      }

      h1, h2, h3, h4, h5, h6 {
        font-family: var(--font-primary);
        color: var(--color-slate-900);
        font-weight: 700;
      }

      /* Glassmorphism utility classes */
      .glassmorphism {
        background: var(--gradient-glassmorphism);
        backdrop-filter: blur(16px);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .glassmorphism-dark {
        background: var(--gradient-glassmorphism-dark);
        backdrop-filter: blur(16px);
        border: 1px solid rgba(0, 0, 0, 0.1);
      }
    </style>
  </head>
  <body>
    <slot />
  </body>
</html>
