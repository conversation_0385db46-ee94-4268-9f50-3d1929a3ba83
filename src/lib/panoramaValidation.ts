/**
 * Utility functions for validating 360° panoramic images
 */

export interface PanoramaValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  metadata?: {
    width: number;
    height: number;
    aspectRatio: number;
    fileSize: number;
    format: string;
  };
}

export interface PanoramaValidationOptions {
  maxFileSize?: number; // in bytes, default 50MB
  minWidth?: number; // minimum width in pixels
  maxWidth?: number; // maximum width in pixels
  allowedFormats?: string[]; // allowed MIME types
  strictAspectRatio?: boolean; // enforce 2:1 aspect ratio
  aspectRatioTolerance?: number; // tolerance for aspect ratio (default 0.1)
}

const DEFAULT_OPTIONS: Required<PanoramaValidationOptions> = {
  maxFileSize: 50 * 1024 * 1024, // 50MB
  minWidth: 2048,
  maxWidth: 8192,
  allowedFormats: ['image/jpeg', 'image/jpg', 'image/png'],
  strictAspectRatio: true,
  aspectRatioTolerance: 0.1
};

/**
 * Validates a file for 360° panoramic image requirements
 */
export async function validatePanoramaFile(
  file: File,
  options: PanoramaValidationOptions = {}
): Promise<PanoramaValidationResult> {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  const errors: string[] = [];
  const warnings: string[] = [];

  // Basic file validation
  if (!file) {
    errors.push('No file provided');
    return { isValid: false, errors, warnings };
  }

  // File size validation
  if (file.size > opts.maxFileSize) {
    errors.push(`File size (${formatFileSize(file.size)}) exceeds maximum allowed size (${formatFileSize(opts.maxFileSize)})`);
  }

  // File type validation
  if (!opts.allowedFormats.includes(file.type)) {
    errors.push(`File format ${file.type} is not supported. Allowed formats: ${opts.allowedFormats.join(', ')}`);
  }

  // Image dimension validation
  try {
    const dimensions = await getImageDimensions(file);
    const aspectRatio = dimensions.width / dimensions.height;
    const expectedAspectRatio = 2.0; // 2:1 for equirectangular

    const metadata = {
      width: dimensions.width,
      height: dimensions.height,
      aspectRatio,
      fileSize: file.size,
      format: file.type
    };

    // Width validation
    if (dimensions.width < opts.minWidth) {
      errors.push(`Image width (${dimensions.width}px) is below minimum required width (${opts.minWidth}px)`);
    }

    if (dimensions.width > opts.maxWidth) {
      warnings.push(`Image width (${dimensions.width}px) is very large. Consider using a smaller image for better performance.`);
    }

    // Aspect ratio validation
    if (opts.strictAspectRatio) {
      const aspectRatioDiff = Math.abs(aspectRatio - expectedAspectRatio);
      if (aspectRatioDiff > opts.aspectRatioTolerance) {
        errors.push(
          `Image aspect ratio (${aspectRatio.toFixed(2)}:1) does not match expected equirectangular format (2:1). ` +
          `Difference: ${aspectRatioDiff.toFixed(2)}, tolerance: ${opts.aspectRatioTolerance}`
        );
      }
    }

    // Performance warnings
    if (file.size > 20 * 1024 * 1024) { // 20MB
      warnings.push('Large file size may affect loading performance. Consider compressing the image.');
    }

    if (dimensions.width > 6000) {
      warnings.push('Very high resolution may affect performance on mobile devices.');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      metadata
    };

  } catch (error) {
    errors.push(`Failed to read image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return { isValid: false, errors, warnings };
  }
}

/**
 * Gets image dimensions from a file
 */
function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(file);

    img.onload = () => {
      URL.revokeObjectURL(url);
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      });
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load image'));
    };

    img.src = url;
  });
}

/**
 * Formats file size in human-readable format
 */
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Validates multiple panorama files
 */
export async function validatePanoramaFiles(
  files: File[],
  options: PanoramaValidationOptions = {}
): Promise<PanoramaValidationResult[]> {
  const validationPromises = files.map(file => validatePanoramaFile(file, options));
  return Promise.all(validationPromises);
}

/**
 * Checks if a file appears to be an equirectangular panorama based on quick checks
 */
export async function quickPanoramaCheck(file: File): Promise<boolean> {
  try {
    // Quick file type check
    if (!file.type.startsWith('image/')) {
      return false;
    }

    // Quick dimension check
    const dimensions = await getImageDimensions(file);
    const aspectRatio = dimensions.width / dimensions.height;
    
    // Allow some tolerance for quick check
    return Math.abs(aspectRatio - 2.0) < 0.3;
  } catch {
    return false;
  }
}

/**
 * Generates validation summary for UI display
 */
export function getValidationSummary(result: PanoramaValidationResult): string {
  if (result.isValid) {
    if (result.warnings.length > 0) {
      return `Valid panorama with ${result.warnings.length} warning(s)`;
    }
    return 'Valid panorama image';
  }

  return `Invalid: ${result.errors.length} error(s) found`;
}

/**
 * Gets recommended image specifications as a formatted string
 */
export function getRecommendedSpecs(): string {
  return `
Recommended specifications for 360° panoramic images:
• Format: JPEG or PNG
• Aspect Ratio: 2:1 (equirectangular projection)
• Resolution: 4096×2048 to 8192×4096 pixels
• File Size: Under 20MB for optimal performance
• Projection: Equirectangular (spherical)
  `.trim();
}

/**
 * Panorama file input validation for forms
 */
export class PanoramaValidator {
  private options: Required<PanoramaValidationOptions>;

  constructor(options: PanoramaValidationOptions = {}) {
    this.options = { ...DEFAULT_OPTIONS, ...options };
  }

  async validate(file: File): Promise<PanoramaValidationResult> {
    return validatePanoramaFile(file, this.options);
  }

  async validateMultiple(files: File[]): Promise<PanoramaValidationResult[]> {
    return validatePanoramaFiles(files, this.options);
  }

  getSpecs(): string {
    return getRecommendedSpecs();
  }

  updateOptions(newOptions: Partial<PanoramaValidationOptions>): void {
    this.options = { ...this.options, ...newOptions };
  }
}
