// Import PocketBase types
import type { RecordModel } from 'pocketbase';

// User type definitions for PocketBase
export interface User extends RecordModel {
  email: string;
  name?: string;
  avatar?: string;
  roles?: string[];
  verified?: boolean;
  emailVisibility?: boolean;
  [key: string]: unknown; // Allow additional fields
}

export interface AuthData {
  token: string;
  record: User;
}

export interface AuthResult {
  success: boolean;
  user?: User;
  token?: string;
  error?: string;
  autoLogin?: boolean;
  message?: string;
}

// PocketBase record model type
export interface PocketBaseRecord {
  id: string;
  created: string;
  updated: string;
  [key: string]: unknown;
}

// Venue related types
export interface Venue extends PocketBaseRecord {
  name: string;
  description: string;
  location: string;
  capacity: number;
  price: number;
  amenities: string[];
  images: string[];
  owner: string; // User ID
  status: 'active' | 'inactive' | 'pending';
}

// Booking related types
export interface Booking extends PocketBaseRecord {
  venue: string; // Venue ID
  renter: string; // User ID
  start_date: string;
  end_date: string;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  total_amount: number;
  message?: string;
}

// Flag content types
export interface FlaggedContent extends PocketBaseRecord {
  content_type: 'venue' | 'user' | 'review' | 'message';
  content_id: string;
  reporter: string; // User ID
  reason: string;
  description?: string;
  status: 'pending' | 'reviewed' | 'resolved' | 'dismissed';
}
