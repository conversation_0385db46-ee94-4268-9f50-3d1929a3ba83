// Message and real-time chat type definitions for Trodoo

export interface Message {
  id: string;
  booking: {
    id: string;
    venue: {
      id: string;
      title: string;
    };
    renter: {
      id: string;
      name: string;
    };
    owner: {
      id: string;
      name: string;
    };
  };
  sender: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  content: string;
  created: string;
  updated: string;
}

export interface MessageCreate {
  booking: string;
  sender: string;
  content: string;
}

export interface MessageUpdate {
  content: string;
}

// Real-time chat types
export interface ChatMessage {
  id: string;
  booking_id: string;
  sender_id: string;
  sender_name: string;
  sender_avatar?: string;
  content: string;
  timestamp: string;
  is_read: boolean;
  is_own_message: boolean;
}

export interface ChatThread {
  booking_id: string;
  messages: ChatMessage[];
  participants: {
    renter: {
      id: string;
      name: string;
      avatar?: string;
    };
    owner: {
      id: string;
      name: string;
      avatar?: string;
    };
  };
  last_message?: ChatMessage;
  unread_count: number;
}

export interface ChatSubscription {
  booking_id: string;
  user_id: string;
  callback: (message: ChatMessage) => void;
  unsubscribe: () => void;
}

// Message form types
export interface MessageFormData {
  content: string;
}

export interface MessageValidationErrors {
  content?: string;
  general?: string;
}

// Real-time events
export interface MessageEvent {
  type: 'message_created' | 'message_updated' | 'message_deleted';
  data: Message;
  booking_id: string;
  sender_id: string;
}

export interface TypingEvent {
  type: 'typing_start' | 'typing_stop';
  booking_id: string;
  user_id: string;
  user_name: string;
}

// API response types
export interface MessageListResponse {
  items: Message[];
  page: number;
  perPage: number;
  totalItems: number;
  totalPages: number;
}

export interface MessageActionResponse {
  success: boolean;
  message?: Message;
  error?: string;
}

// Chat UI state types
export interface ChatUIState {
  isLoading: boolean;
  isConnected: boolean;
  isTyping: boolean;
  typingUsers: string[];
  error?: string;
  lastSeen?: string;
}

export interface MessageStatus {
  id: string;
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  timestamp: string;
}

// Notification types for messages
export interface MessageNotification {
  id: string;
  booking_id: string;
  sender_name: string;
  venue_title: string;
  content_preview: string;
  timestamp: string;
  is_read: boolean;
}
