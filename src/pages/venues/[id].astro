---
import BaseLayout from '../../layouts/BaseLayout.astro';
import FlagButton from '../../components/common/FlagButton.tsx';
import PannellumViewer from '../../components/venues/PannellumViewer.tsx';
import { getPocketBase } from '../../lib/pocketbase.ts';

export async function getStaticPaths() {
  // For SSR, we return an empty array since paths will be generated on-demand
  return [];
}

const { id } = Astro.params;

if (!id) {
  return Astro.redirect('/venues');
}

let venue: any = null;
let error: string | null = null;

try {
  const pb = getPocketBase();
  venue = await pb.collection('venues').getOne(id, {
    expand: 'owner'
  });
  
  // Check if venue is published or if user is the owner
  if (!venue.is_published) {
    // In a real app, you'd check if the current user is the owner
    // For now, we'll redirect to 404
    return Astro.redirect('/404');
  }
} catch (err) {
  console.error('Failed to fetch venue:', err);
  error = 'Venue not found';
}

if (error || !venue) {
  return Astro.redirect('/404');
}

// Transform venue data to match our TypeScript interface
const venueData = {
  id: venue.id,
  title: venue.title,
  description: venue.description,
  address: venue.address,
  capacity: venue.capacity,
  price_per_hour: venue.price_per_hour,
  amenities: venue.amenities || [],
  standard_photos: venue.standard_photos || [],
  pano_photo: venue.pano_photo,
  rental_agreement_pdf: venue.rental_agreement_pdf,
  is_published: venue.is_published,
  owner: venue.expand?.owner ? {
    id: venue.expand.owner.id,
    name: venue.expand.owner.name,
    email: venue.expand.owner.email,
    avatar: venue.expand.owner.avatar
  } : { id: venue.owner, name: 'Unknown', email: '' },
  average_rating: venue.average_rating || 0,
  review_count: venue.review_count || 0,
  total_bookings: venue.total_bookings || 0,
  created: venue.created,
  updated: venue.updated
};

const formatPrice = (price: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
  }).format(price);
};

const formatAddress = (address: any) => {
  if (typeof address === 'string') return address;
  if (!address) return 'Address not available';
  return `${address.street}, ${address.city}, ${address.country}`;
};
---

<BaseLayout
  title={`${venueData.title} - Trodoo`}
  description={venueData.description}
>
  <!-- Venue data for client-side JavaScript -->
  <meta name="venue-id" content={venueData.id} slot="head" />
  <meta name="venue-title" content={venueData.title} slot="head" />
  <meta name="venue-price" content={venueData.price_per_hour.toString()} slot="head" />
  <meta name="venue-owner-id" content={venueData.owner.id} slot="head" />
  <meta name="venue-owner-name" content={venueData.owner.name} slot="head" />
  <meta name="venue-owner-email" content={venueData.owner.email} slot="head" />
  <div class="min-h-screen bg-slate-50">
    <!-- 360° Photo Viewer Section -->
    {venueData.pano_photo && (
      <div class="h-96">
        <PannellumViewer
          client:load
          imageUrl={venueData.pano_photo}
          title={`${venueData.title} - 360° View`}
          height="24rem"
          autoLoad={true}
          showControls={true}
          onLoad={() => console.log('360° view loaded')}
          onError={(error) => console.error('360° view error:', error)}
        />
      </div>
    )}

    <!-- Standard Photo Gallery -->
    {!venueData.pano_photo && venueData.standard_photos.length > 0 && (
      <div class="h-96 bg-slate-200 relative overflow-hidden">
        <img
          src={venueData.standard_photos[0]}
          alt={venueData.title}
          class="w-full h-full object-cover"
        />
        {venueData.standard_photos.length > 1 && (
          <div class="absolute bottom-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
            +{venueData.standard_photos.length - 1} more photos
          </div>
        )}
      </div>
    )}

    <!-- Content Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-8">
          <!-- Header -->
          <div>
            <h1 class="text-3xl font-bold text-slate-900 mb-2">
              {venueData.title}
            </h1>
            <div class="flex items-center text-slate-600 mb-4">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              {formatAddress(venueData.address)}
            </div>
            
            <div class="flex items-center space-x-6 text-sm text-slate-600">
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-1 text-primary-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                Up to {venueData.capacity} guests
              </div>
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-1 text-primary-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
                {formatPrice(venueData.price_per_hour)}/hour
              </div>
            </div>
          </div>

          <!-- Description -->
          <div class="prose prose-slate max-w-none">
            <h2 class="text-xl font-semibold text-slate-900 mb-4">About this venue</h2>
            <div class="text-slate-700 leading-relaxed" set:html={venueData.description}></div>
          </div>

          <!-- Amenities -->
          {venueData.amenities.length > 0 && (
            <div>
              <h2 class="text-xl font-semibold text-slate-900 mb-4">Amenities</h2>
              <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                {venueData.amenities.map((amenity: string) => (
                  <div class="flex items-center p-3 bg-white rounded-lg border border-slate-200">
                    <svg class="w-5 h-5 text-primary-green mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    <span class="text-sm text-slate-700">{amenity}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          <!-- Photo Gallery -->
          {venueData.standard_photos.length > 1 && (
            <div>
              <h2 class="text-xl font-semibold text-slate-900 mb-4">Photo Gallery</h2>
              <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                {venueData.standard_photos.slice(1).map((photo: string, index: number) => (
                  <div class="aspect-square bg-slate-200 rounded-lg overflow-hidden">
                    <img
                      src={photo}
                      alt={`${venueData.title} photo ${index + 2}`}
                      class="w-full h-full object-cover hover:scale-105 transition-transform duration-300 cursor-pointer"
                    />
                  </div>
                ))}
              </div>
            </div>
          )}

          <!-- Host Information -->
          <div class="bg-white rounded-xl p-6 border border-slate-200">
            <h2 class="text-xl font-semibold text-slate-900 mb-4">Meet your host</h2>
            <div class="flex items-center space-x-4">
              <div class="w-16 h-16 bg-primary-green rounded-full flex items-center justify-center text-white text-xl font-semibold">
                {venueData.owner.name.charAt(0).toUpperCase()}
              </div>
              <div>
                <h3 class="font-semibold text-slate-900">{venueData.owner.name}</h3>
                <p class="text-sm text-slate-600">Venue host since {new Date(venueData.created).getFullYear()}</p>
              </div>
            </div>
          </div>

          <!-- Flag Button -->
          <div class="flex justify-start">
            <FlagButton 
              client:load
              contentType="venue"
              contentId={venueData.id}
              className="text-sm text-slate-500 hover:text-slate-700"
            />
          </div>
        </div>

        <!-- Booking Sidebar -->
        <div class="lg:col-span-1">
          <div id="booking-form-container">
            <!-- BookingForm component will be mounted here -->
            <div class="bg-white rounded-xl shadow-card border border-slate-200 p-6">
              <div class="animate-pulse space-y-4">
                <div class="h-6 bg-slate-200 rounded w-1/2"></div>
                <div class="h-10 bg-slate-200 rounded"></div>
                <div class="h-10 bg-slate-200 rounded"></div>
                <div class="h-12 bg-slate-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</BaseLayout>

<!-- React Component Integration -->
<script>
  import BookingForm from '../../components/venues/BookingForm.tsx';
  import { createRoot } from 'react-dom/client';
  import React from 'react';
  import { createBooking } from '../../lib/pocketbase.ts';
  import { calculateFees } from '../../lib/paystack.ts';

  // Venue data from server
  const venueData = {
    id: document.querySelector('meta[name="venue-id"]')?.getAttribute('content'),
    title: document.querySelector('meta[name="venue-title"]')?.getAttribute('content'),
    price_per_hour: parseFloat(document.querySelector('meta[name="venue-price"]')?.getAttribute('content') || '0'),
    owner: {
      id: document.querySelector('meta[name="venue-owner-id"]')?.getAttribute('content'),
      name: document.querySelector('meta[name="venue-owner-name"]')?.getAttribute('content'),
      email: document.querySelector('meta[name="venue-owner-email"]')?.getAttribute('content')
    }
  };

  document.addEventListener('DOMContentLoaded', async () => {
    console.log('Venue detail page loaded');

    // Check if user is authenticated
    let currentUserId = null;
    try {
      const authData = localStorage.getItem('pocketbase_auth');
      if (authData) {
        const parsed = JSON.parse(authData);
        currentUserId = parsed.model?.id;
      }
    } catch (error) {
      console.error('Failed to get user ID:', error);
    }

    // Mount BookingForm component
    const container = document.getElementById('booking-form-container');
    if (container) {
      const root = createRoot(container);

      const handleBookingSubmit = async (bookingData: any) => {
        try {
          if (!currentUserId) {
            // Redirect to login if not authenticated
            window.location.href = '/auth/login?redirect=' + encodeURIComponent(window.location.pathname);
            return;
          }

          // Calculate fees
          const startDateTime = new Date(`${bookingData.start_date}T${bookingData.start_time}`);
          const endDateTime = new Date(`${bookingData.end_date}T${bookingData.end_time}`);
          const hours = (endDateTime.getTime() - startDateTime.getTime()) / (1000 * 60 * 60);
          const subtotal = hours * venueData.price_per_hour;
          const feeCalculation = calculateFees(subtotal);

          // Create booking request
          const bookingRequest = {
            venue: venueData.id,
            owner: venueData.owner.id,
            start_date: `${bookingData.start_date}T${bookingData.start_time}:00.000Z`,
            end_date: `${bookingData.end_date}T${bookingData.end_time}:00.000Z`,
            total_price: feeCalculation.total_amount,
            platform_fee: feeCalculation.platform_fee,
            payout_amount: feeCalculation.payout_amount,
            special_requests: bookingData.special_requests || ''
          };

          const result = await createBooking(bookingRequest);

          if (result.success && result.booking) {
            // Redirect to booking details page
            window.location.href = `/bookings/${result.booking.id}`;
          } else {
            alert('Failed to create booking: ' + (result.error || 'Unknown error'));
          }
        } catch (error) {
          console.error('Booking submission error:', error);
          alert('Failed to submit booking request. Please try again.');
        }
      };

      // Get venue data from the page - create minimal venue object for BookingForm
      const venue = {
        id: venueData.id || '',
        title: venueData.title || '',
        description: '', // Not needed for BookingForm
        address: { street: '', city: '', country: '' }, // Not needed for BookingForm
        capacity: 0, // Not needed for BookingForm
        price_per_hour: venueData.price_per_hour,
        amenities: [], // Not needed for BookingForm
        standard_photos: [], // Not needed for BookingForm
        pano_photo: undefined,
        rental_agreement_pdf: undefined,
        is_published: true,
        owner: {
          id: venueData.owner.id || '',
          name: venueData.owner.name || '',
          email: venueData.owner.email || '',
          avatar: undefined
        },
        average_rating: 0,
        review_count: 0,
        total_bookings: 0,
        created: '',
        updated: ''
      };

      root.render(
        React.createElement(BookingForm, {
          venue: venue,
          onSubmit: handleBookingSubmit,
          isSticky: true
        })
      );
    }
  });
</script>

<style>
  /* Custom styles for the venue detail page */
  .prose h2 {
    @apply text-xl font-semibold text-slate-900 mb-4;
  }
  
  .prose p {
    @apply text-slate-700 leading-relaxed mb-4;
  }
</style>
