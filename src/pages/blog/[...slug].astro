---
import { getCollection, render } from 'astro:content';
import BaseLayout from '@/layouts/BaseLayout.astro';
import Header from '../../components/core/Header.astro';
import Footer from '@/components/Footer/Footer.tsx';

export const prerender = true;

export async function getStaticPaths() {
  const posts = await getCollection('blog');
  return posts.map(post => ({
    params: { slug: post.slug },
    props: { post },
  }));
}

const { post } = Astro.props;
const { Content } = await render(post);

// Format the publication date
const formattedDate = post.data.pubDate.toLocaleDateString('en-US', {
  year: 'numeric',
  month: 'long',
  day: 'numeric'
});
---

<BaseLayout 
  title={`${post.data.title} - Trodoo Blog`}
  description={post.data.description}
  image={post.data.heroImage}
>
  <Header />
  
  <main>
    <!-- Article Header -->
    <article class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <header class="mb-8">
        <!-- Breadcrumb -->
        <nav class="flex items-center text-sm text-secondary-500 mb-6">
          <a href="/" class="hover:text-primary-600 transition-colors">Home</a>
          <svg class="mx-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
          <a href="/blog" class="hover:text-primary-600 transition-colors">Blog</a>
          <svg class="mx-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
          <span class="text-secondary-700">{post.data.title}</span>
        </nav>
        
        <!-- Article Title -->
        <h1 class="text-4xl md:text-5xl font-bold text-secondary-900 mb-6 leading-tight">
          {post.data.title}
        </h1>
        
        <!-- Article Meta -->
        <div class="flex items-center text-secondary-600 mb-8">
          <div class="flex items-center">
            <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center mr-3">
              <span class="text-primary-600 font-semibold text-sm">
                {post.data.author.charAt(0).toUpperCase()}
              </span>
            </div>
            <div>
              <p class="font-medium text-secondary-900">{post.data.author}</p>
              <time datetime={post.data.pubDate.toISOString()} class="text-sm">
                {formattedDate}
              </time>
            </div>
          </div>
        </div>
        
        <!-- Hero Image -->
        {post.data.heroImage && (
          <div class="aspect-video rounded-lg overflow-hidden mb-8">
            <img 
              src={post.data.heroImage} 
              alt={post.data.title}
              class="w-full h-full object-cover"
            />
          </div>
        )}
        
        <!-- Article Description -->
        <p class="text-xl text-secondary-600 leading-relaxed border-l-4 border-primary-200 pl-6 mb-8">
          {post.data.description}
        </p>
      </header>
      
      <!-- Article Content -->
      <div class="prose prose-lg prose-secondary max-w-none">
        <Content />
      </div>
      
      <!-- Article Footer -->
      <footer class="mt-16 pt-8 border-t border-secondary-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <span class="text-secondary-600 mr-4">Share this article:</span>
            <div class="flex space-x-3">
              <a 
                href={`https://twitter.com/intent/tweet?text=${encodeURIComponent(post.data.title)}&url=${encodeURIComponent(Astro.url.toString())}`}
                target="_blank"
                rel="noopener noreferrer"
                class="text-secondary-400 hover:text-primary-600 transition-colors"
                aria-label="Share on Twitter"
              >
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"/>
                </svg>
              </a>
              <a 
                href={`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(Astro.url.toString())}`}
                target="_blank"
                rel="noopener noreferrer"
                class="text-secondary-400 hover:text-primary-600 transition-colors"
                aria-label="Share on LinkedIn"
              >
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </a>
            </div>
          </div>
          
          <a 
            href="/blog"
            class="inline-flex items-center text-primary-600 hover:text-primary-700 font-medium transition-colors"
          >
            <svg class="mr-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
            Back to Blog
          </a>
        </div>
      </footer>
    </article>
  </main>
  
  <Footer />
</BaseLayout>

<style>
  .prose {
    @apply text-secondary-700;
  }
  
  .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    @apply text-secondary-900 font-semibold;
  }
  
  .prose h2 {
    @apply text-2xl mt-8 mb-4;
  }
  
  .prose h3 {
    @apply text-xl mt-6 mb-3;
  }
  
  .prose p {
    @apply mb-4 leading-relaxed;
  }
  
  .prose a {
    @apply text-primary-600 hover:text-primary-700 transition-colors;
  }
  
  .prose ul, .prose ol {
    @apply mb-4 pl-6;
  }
  
  .prose li {
    @apply mb-2;
  }
  
  .prose blockquote {
    @apply border-l-4 border-primary-200 pl-6 italic text-secondary-600 my-6;
  }
  
  .prose code {
    @apply bg-secondary-100 px-2 py-1 rounded text-sm font-mono;
  }
  
  .prose pre {
    @apply bg-secondary-900 text-white p-4 rounded-lg overflow-x-auto my-6;
  }
  
  .prose pre code {
    @apply bg-transparent p-0;
  }
</style>
