---
import Layout from '../../components/core/Layout.astro';
---

<Layout
  title="Jo<PERSON> - Create Account"
  description="Join <PERSON><PERSON><PERSON> to discover and book unique venues for your special events."
  showHeader={false}
  showFooter={false}
>
  <div class="min-h-screen flex font-['Roboto','Helvetica_Neue',Arial,sans-serif]">
    <!-- Left Column - Desktop Only -->
    <div class="hidden lg:flex lg:w-1/2 relative overflow-hidden" style="background: linear-gradient(135deg, #059669 0%, #10B981 100%);">
      <!-- GridMotion Background Pattern -->
      <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25px 25px, rgba(5, 150, 105, 0.3) 2px, transparent 0), radial-gradient(circle at 75px 75px, rgba(5, 150, 105, 0.3) 2px, transparent 0); background-size: 100px 100px;"></div>
      </div>

      <!-- Glassmorphism Overlay -->
      <div class="absolute inset-0" style="background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(16px);"></div>

      <!-- Content -->
      <div class="relative z-10 flex flex-col justify-center px-12 py-16">
        <div class="max-w-md">
          <!-- Logo with Back Button -->
          <div class="flex items-center justify-between mb-8">
            <a href="/" class="inline-block transition-all duration-300 hover:scale-105">
              <h1 class="text-4xl font-bold text-white font-['Anton',sans-serif] tracking-wide">Trodoo</h1>
            </a>
            <button
              onclick="history.back()"
              class="flex items-center space-x-2 px-4 py-2 rounded-xl transition-all duration-300 hover:scale-105"
              style="background: rgba(255, 255, 255, 0.2); backdrop-filter: blur(12px); border: 1px solid rgba(255, 255, 255, 0.3);"
              onmouseover="this.style.background='rgba(255, 255, 255, 0.3)'"
              onmouseout="this.style.background='rgba(255, 255, 255, 0.2)'"
            >
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
              </svg>
              <span class="text-white font-medium text-sm">Back</span>
            </button>
          </div>

          <!-- Tagline -->
          <h2 class="text-4xl font-bold text-white mb-6 leading-tight font-['Poppins','Helvetica_Neue',Arial,sans-serif]">
            Start Your Journey
          </h2>

          <p class="text-xl text-white/90 mb-8 leading-relaxed font-normal">
            Join thousands of event organizers who trust Trodoo to find the perfect venues for their special occasions.
          </p>

          <!-- Benefits -->
          <div class="space-y-4">
            <div class="flex items-center space-x-3 group">
              <div class="w-2 h-2 bg-white rounded-full group-hover:scale-125 transition-transform duration-300"></div>
              <span class="text-white/90 text-base">Access to premium venues nationwide</span>
            </div>
            <div class="flex items-center space-x-3 group">
              <div class="w-2 h-2 bg-white rounded-full group-hover:scale-125 transition-transform duration-300"></div>
              <span class="text-white/90 text-base">Secure booking and payment processing</span>
            </div>
            <div class="flex items-center space-x-3 group">
              <div class="w-2 h-2 bg-white rounded-full group-hover:scale-125 transition-transform duration-300"></div>
              <span class="text-white/90 text-base">24/7 customer support</span>
            </div>
            <div class="flex items-center space-x-3 group">
              <div class="w-2 h-2 bg-white rounded-full group-hover:scale-125 transition-transform duration-300"></div>
              <span class="text-white/90 text-base">List your own venues and earn</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Decorative GridMotion Pattern -->
      <div class="absolute bottom-0 right-0 w-64 h-64 opacity-20">
        <svg viewBox="0 0 200 200" class="w-full h-full">
          <defs>
            <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
              <path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(5, 150, 105, 0.5)" stroke-width="1"/>
            </pattern>
          </defs>
          <rect width="200" height="200" fill="url(#grid)" />
        </svg>
      </div>
    </div>

    <!-- Right Column - Form -->
    <div class="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12" style="background: linear-gradient(135deg, #F9FAFB 0%, #F3F4F6 100%);">
      <div class="max-w-md w-full space-y-8">
        <!-- Mobile Logo with Back Button -->
        <div class="lg:hidden">
          <div class="flex items-center justify-between mb-4">
            <button
              onclick="history.back()"
              class="flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-300 hover:scale-105"
              style="background-color: #F1F5F9; border: 1px solid #E2E8F0;"
              onmouseover="this.style.backgroundColor='#E2E8F0'; this.style.borderColor='#CBD5E1'"
              onmouseout="this.style.backgroundColor='#F1F5F9'; this.style.borderColor='#E2E8F0'"
            >
              <svg class="w-4 h-4" style="color: #059669;" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
              </svg>
              <span class="font-medium text-sm" style="color: #059669;">Back</span>
            </button>
            <a href="/" class="inline-block transition-all duration-300 hover:scale-105">
              <h1 class="text-3xl font-bold font-['Anton',sans-serif] tracking-wide" style="color: #059669;">Trodoo</h1>
            </a>
            <div class="w-16"></div> <!-- Spacer for centering -->
          </div>
        </div>

        <!-- Header -->
        <div class="text-center">
          <h2 class="text-4xl font-bold leading-tight font-['Poppins','Helvetica_Neue',Arial,sans-serif]" style="color: #0F172A;">
            Create Your Account
          </h2>
          <p class="mt-3 text-base leading-relaxed" style="color: #64748B;">
            Already have an account?
            <a href="/auth/login" class="font-medium ml-1 transition-all duration-300 hover:scale-105 inline-block" style="color: #059669;" onmouseover="this.style.color='#047857'" onmouseout="this.style.color='#059669'">
              Sign In
            </a>
          </p>
        </div>

        <!-- Registration Form Container -->
        <div class="bg-white rounded-xl p-8 border border-slate-200 transition-all duration-300 hover:shadow-lg hover:-translate-y-1" style="box-shadow: 0 8px 16px rgba(0,0,0,0.1);">
          <div id="register-form-container">
            <!-- Enhanced form will be rendered here -->
          </div>
        </div>
      </div>
    </div>
  </div>
</Layout>

<script>
  import { authActions } from '../../lib/state.js';

  // Enhanced registration form with proper authentication
  document.addEventListener('DOMContentLoaded', async () => {
    const container = document.getElementById('register-form-container');
    if (!container) return;

    let isLoading = false;
    let error = '';
    let showPassword = false;
    let showPasswordConfirm = false;
    let formData = {
      name: '',
      email: '',
      password: '',
      passwordConfirm: '',
      agreeToTerms: false
    };

    const getPasswordStrength = (password: string) => {
      let score = 0;
      const feedback = [];

      if (password.length >= 8) score += 1;
      else feedback.push('At least 8 characters');

      if (/(?=.*[a-z])/.test(password)) score += 1;
      else feedback.push('One lowercase letter');

      if (/(?=.*[A-Z])/.test(password)) score += 1;
      else feedback.push('One uppercase letter');

      if (/(?=.*\d)/.test(password)) score += 1;
      else feedback.push('One number');

      if (/(?=.*[!@#$%^&*(),.?":{}|<>])/.test(password)) {
        score += 1;
      } else {
        feedback.push('One special character (optional)');
      }

      let color = '#DC2626'; // red
      if (score >= 4) color = '#059669'; // primary green
      else if (score >= 3) color = '#F59E0B'; // secondary yellow

      return { score, feedback, color };
    };

    const updatePasswordStrength = () => {
      const passwordStrengthContainer = document.querySelector('.password-strength-container');
      if (passwordStrengthContainer && formData.password) {
        const passwordStrength = getPasswordStrength(formData.password);
        passwordStrengthContainer.innerHTML = `
          <div class="mt-3 space-y-2">
            <div class="flex items-center space-x-2">
              <div class="flex-1 rounded-full h-2" style="background-color: #E2E8F0;">
                <div
                  class="h-2 rounded-full transition-all duration-300"
                  style="width: ${(passwordStrength.score / 5) * 100}%; background-color: ${passwordStrength.color};"
                ></div>
              </div>
              <span class="text-xs font-medium" style="color: ${passwordStrength.color}; font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">
                ${passwordStrength.score < 3 ? 'Weak' : passwordStrength.score < 4 ? 'Good' : 'Strong'}
              </span>
            </div>
            ${passwordStrength.feedback.length > 0 ? `
              <div class="text-xs" style="color: #64748B; font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">
                <p class="mb-1">Password must include:</p>
                <ul class="space-y-1">
                  ${passwordStrength.feedback.map(item => `
                    <li class="flex items-center space-x-2">
                      <span class="w-1 h-1 rounded-full" style="background-color: #64748B;"></span>
                      <span>${item}</span>
                    </li>
                  `).join('')}
                </ul>
              </div>
            ` : ''}
          </div>
        `;
      } else if (passwordStrengthContainer && !formData.password) {
        passwordStrengthContainer.innerHTML = '';
      }
    };

    const updateButtonState = () => {
      const submitButton = document.querySelector('button[type="submit"]') as HTMLButtonElement;
      if (submitButton) {
        const isDisabled = isLoading || !formData.agreeToTerms;
        submitButton.disabled = isDisabled;

        // Update button styling based on disabled state
        if (isDisabled) {
          submitButton.style.opacity = '0.5';
          submitButton.style.cursor = 'not-allowed';
          submitButton.style.backgroundColor = '#F59E0B';
        } else {
          submitButton.style.opacity = '1';
          submitButton.style.cursor = 'pointer';
          submitButton.style.backgroundColor = '#F59E0B';
        }
      }
    };

    const updatePasswordToggleIcon = (buttonId: string, isVisible: boolean) => {
      const button = document.getElementById(buttonId);
      if (button) {
        const svg = button.querySelector('svg');
        if (svg) {
          svg.innerHTML = isVisible ? `
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
          ` : `
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
          `;
        }
      }
    };

    const renderForm = () => {
      const passwordStrength = getPasswordStrength(formData.password);

      container.innerHTML = `
        <form id="register-form" class="space-y-6">
          <div>
            <label for="name" class="block text-sm font-medium mb-2" style="color: #334155; font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">
              Full Name
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value="${formData.name}"
              required
              class="w-full px-4 py-3 border rounded-lg bg-white text-base transition-all duration-300 ease-in-out focus:outline-none ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}"
              style="border-color: #CBD5E1; font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;"
              onfocus="this.style.borderColor='#059669'; this.style.boxShadow='0 0 0 3px rgba(5, 150, 105, 0.1)'"
              onblur="this.style.borderColor='#CBD5E1'; this.style.boxShadow='none'"
              placeholder="Enter your full name"
              ${isLoading ? 'disabled' : ''}
            />
          </div>

          <div>
            <label for="email" class="block text-sm font-medium mb-2" style="color: #334155; font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">
              Email Address
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value="${formData.email}"
              required
              class="w-full px-4 py-3 border rounded-lg bg-white text-base transition-all duration-300 ease-in-out focus:outline-none ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}"
              style="border-color: #CBD5E1; font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;"
              onfocus="this.style.borderColor='#059669'; this.style.boxShadow='0 0 0 3px rgba(5, 150, 105, 0.1)'"
              onblur="this.style.borderColor='#CBD5E1'; this.style.boxShadow='none'"
              placeholder="Enter your email"
              ${isLoading ? 'disabled' : ''}
            />
          </div>

          <div>
            <label for="password" class="block text-sm font-medium mb-2" style="color: #334155; font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">
              Password
            </label>
            <div class="relative">
              <input
                type="${showPassword ? 'text' : 'password'}"
                id="password"
                name="password"
                value="${formData.password}"
                required
                class="w-full px-4 py-3 pr-12 border rounded-lg bg-white text-base transition-all duration-300 ease-in-out focus:outline-none ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}"
                style="border-color: #CBD5E1; font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;"
                onfocus="this.style.borderColor='#059669'; this.style.boxShadow='0 0 0 3px rgba(5, 150, 105, 0.1)'"
                onblur="this.style.borderColor='#CBD5E1'; this.style.boxShadow='none'"
                placeholder="Create a password"
                ${isLoading ? 'disabled' : ''}
              />
              <button
                type="button"
                id="toggle-password"
                class="absolute inset-y-0 right-0 flex items-center pr-3 transition-colors duration-300"
                style="color: #6B7280;"
                onmouseover="this.style.color='#059669'"
                onmouseout="this.style.color='#6B7280'"
                ${isLoading ? 'disabled' : ''}
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                  ${showPassword ? `
                    <path stroke-linecap="round" stroke-linejoin="round" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                  ` : `
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  `}
                </svg>
              </button>
            </div>
            <div class="password-strength-container">
              ${formData.password ? `
                <div class="mt-3 space-y-2">
                  <div class="flex items-center space-x-2">
                    <div class="flex-1 rounded-full h-2" style="background-color: #E2E8F0;">
                      <div
                        class="h-2 rounded-full transition-all duration-300"
                        style="width: ${(passwordStrength.score / 5) * 100}%; background-color: ${passwordStrength.color};"
                      ></div>
                    </div>
                    <span class="text-xs font-medium" style="color: ${passwordStrength.color}; font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">
                      ${passwordStrength.score < 3 ? 'Weak' : passwordStrength.score < 4 ? 'Good' : 'Strong'}
                    </span>
                  </div>
                  ${passwordStrength.feedback.length > 0 ? `
                    <div class="text-xs" style="color: #64748B; font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">
                      <p class="mb-1">Password must include:</p>
                      <ul class="space-y-1">
                        ${passwordStrength.feedback.map(item => `
                          <li class="flex items-center space-x-2">
                            <span class="w-1 h-1 rounded-full" style="background-color: #64748B;"></span>
                            <span>${item}</span>
                          </li>
                        `).join('')}
                      </ul>
                    </div>
                  ` : ''}
                </div>
              ` : ''}
            </div>
          </div>

          <div>
            <label for="passwordConfirm" class="block text-sm font-medium mb-2" style="color: #334155; font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">
              Confirm Password
            </label>
            <div class="relative">
              <input
                type="${showPasswordConfirm ? 'text' : 'password'}"
                id="passwordConfirm"
                name="passwordConfirm"
                value="${formData.passwordConfirm}"
                required
                class="w-full px-4 py-3 pr-12 border rounded-lg bg-white text-base transition-all duration-300 ease-in-out focus:outline-none ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}"
                style="border-color: #CBD5E1; font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;"
                onfocus="this.style.borderColor='#059669'; this.style.boxShadow='0 0 0 3px rgba(5, 150, 105, 0.1)'"
                onblur="this.style.borderColor='#CBD5E1'; this.style.boxShadow='none'"
                placeholder="Confirm your password"
                ${isLoading ? 'disabled' : ''}
              />
              <button
                type="button"
                id="toggle-password-confirm"
                class="absolute inset-y-0 right-0 flex items-center pr-3 transition-colors duration-300"
                style="color: #6B7280;"
                onmouseover="this.style.color='#059669'"
                onmouseout="this.style.color='#6B7280'"
                ${isLoading ? 'disabled' : ''}
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                  ${showPasswordConfirm ? `
                    <path stroke-linecap="round" stroke-linejoin="round" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                  ` : `
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  `}
                </svg>
              </button>
            </div>
          </div>

          <div class="flex items-start space-x-3">
            <input
              type="checkbox"
              id="agreeToTerms"
              name="agreeToTerms"
              ${formData.agreeToTerms ? 'checked' : ''}
              class="mt-1 h-4 w-4 rounded transition-all duration-300"
              style="color: #059669; border-color: #CBD5E1; accent-color: #059669;"
              onfocus="this.style.boxShadow='0 0 0 2px rgba(5, 150, 105, 0.2)'"
              onblur="this.style.boxShadow='none'"
              ${isLoading ? 'disabled' : ''}
            />
            <label for="agreeToTerms" class="text-sm" style="color: #64748B; font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">
              I agree to the
              <a href="/terms" class="font-medium transition-colors duration-300" style="color: #059669;" onmouseover="this.style.color='#047857'" onmouseout="this.style.color='#059669'">
                Terms of Service
              </a>
              and
              <a href="/privacy" class="font-medium transition-colors duration-300" style="color: #059669;" onmouseover="this.style.color='#047857'" onmouseout="this.style.color='#059669'">
                Privacy Policy
              </a>
            </label>
          </div>

          ${error ? `
            <div class="p-4 rounded-lg animate-fade-in transition-all duration-300" style="background-color: #FEF2F2; border: 1px solid #FECACA;">
              <p class="text-sm" style="color: #DC2626; font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">${error}</p>
            </div>
          ` : ''}

          <button
            type="submit"
            ${isLoading || !formData.agreeToTerms ? 'disabled' : ''}
            class="w-full flex justify-center items-center py-3 px-6 border-0 rounded-full text-base font-bold transition-all duration-300 ease-in-out transform disabled:opacity-50 disabled:cursor-not-allowed"
            style="background-color: #F59E0B; color: #1F2937; font-family: 'Poppins', 'Helvetica Neue', Arial, sans-serif; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);"
            onmouseover="if(!this.disabled) { this.style.backgroundColor='#D97706'; this.style.transform='scale(1.02)'; this.style.boxShadow='0 10px 20px rgba(0, 0, 0, 0.15)'; }"
            onmouseout="if(!this.disabled) { this.style.backgroundColor='#F59E0B'; this.style.transform='scale(1)'; this.style.boxShadow='0 4px 8px rgba(0, 0, 0, 0.1)'; }"
            onmousedown="if(!this.disabled) { this.style.transform='scale(0.98)'; }"
            onmouseup="if(!this.disabled) { this.style.transform='scale(1.02)'; }"
            onfocus="this.style.outline='none'; this.style.boxShadow='0 0 0 3px rgba(245, 158, 11, 0.2), 0 4px 8px rgba(0, 0, 0, 0.1)';"
            onblur="this.style.boxShadow='0 4px 8px rgba(0, 0, 0, 0.1)';"
          >
            ${isLoading ? `
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5" style="color: #1F2937;" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Creating account...
            ` : 'Create Account'}
          </button>
        </form>
      `;

      // Attach event listeners
      const form = document.getElementById('register-form');
      if (form) {
        form.addEventListener('submit', handleSubmit);

        // Add input listeners for real-time updates
        ['name', 'email', 'password', 'passwordConfirm'].forEach(field => {
          const input = form.querySelector(`[name="${field}"]`) as HTMLInputElement;
          if (input) {
            input.addEventListener('input', (e) => {
              const target = e.target as HTMLInputElement;
              (formData as any)[field] = target.value;
              if (field === 'password') {
                updatePasswordStrength(); // Update password strength without full re-render
              }
            });
          }
        });

        const checkbox = form.querySelector('[name="agreeToTerms"]') as HTMLInputElement;
        if (checkbox) {
          checkbox.addEventListener('change', (e) => {
            const target = e.target as HTMLInputElement;
            formData.agreeToTerms = target.checked;
            updateButtonState(); // Update button state without full re-render
          });
        }

        // Add password toggle listeners
        const togglePassword = document.getElementById('toggle-password');
        if (togglePassword) {
          togglePassword.addEventListener('click', () => {
            showPassword = !showPassword;
            const passwordInput = document.getElementById('password') as HTMLInputElement;
            if (passwordInput) {
              passwordInput.type = showPassword ? 'text' : 'password';
              updatePasswordToggleIcon('toggle-password', showPassword);
            }
          });
        }

        const togglePasswordConfirm = document.getElementById('toggle-password-confirm');
        if (togglePasswordConfirm) {
          togglePasswordConfirm.addEventListener('click', () => {
            showPasswordConfirm = !showPasswordConfirm;
            const passwordConfirmInput = document.getElementById('passwordConfirm') as HTMLInputElement;
            if (passwordConfirmInput) {
              passwordConfirmInput.type = showPasswordConfirm ? 'text' : 'password';
              updatePasswordToggleIcon('toggle-password-confirm', showPasswordConfirm);
            }
          });
        }
      }
    };

    const validateForm = () => {
      if (!formData.name.trim()) return 'Name is required';
      if (!formData.email) return 'Email is required';
      if (!/\S+@\S+\.\S+/.test(formData.email)) return 'Please enter a valid email address';

      const passwordStrength = getPasswordStrength(formData.password);
      if (passwordStrength.score < 4) return 'Password must meet the requirements';

      if (formData.password !== formData.passwordConfirm) return 'Passwords do not match';
      if (!formData.agreeToTerms) return 'You must agree to the Terms of Service';

      return null;
    };

    const handleSubmit = async (e: Event) => {
      e.preventDefault();

      const validationError = validateForm();
      if (validationError) {
        error = validationError;
        renderForm();
        return;
      }

      isLoading = true;
      error = '';
      renderForm();

      try {
        const result = await authActions.register({
          name: formData.name,
          email: formData.email,
          password: formData.password,
          passwordConfirm: formData.passwordConfirm
        });

        if (result.success) {
          if (result.autoLogin) {
            // Auto-login was successful, redirect to dashboard
            window.location.href = '/dashboard?welcome=true';
          } else {
            // Registration successful but auto-login failed, redirect to login
            window.location.href = '/auth/login?message=' + encodeURIComponent(result.message || 'Account created successfully! Please sign in.');
          }
        } else {
          error = result.error || 'Registration failed. Please try again.';
          isLoading = false;
          renderForm();
        }
      } catch (err) {
        console.error('Registration error:', err);
        error = 'An unexpected error occurred. Please try again.';
        isLoading = false;
        renderForm();
      }
    };

    // Initial render
    renderForm();
  });
</script>
