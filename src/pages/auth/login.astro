---
import Layout from '../../components/core/Layout.astro';
---

<Layout
  title="Welcome Back - Trodoo"
  description="Sign in to your Trodoo account to manage your bookings and venues."
  showHeader={false}
  showFooter={false}
>
  <div class="min-h-screen flex font-['Roboto','Helvetica_Neue',Arial,sans-serif]">
    <!-- Left Column - Desktop Only -->
    <div class="hidden lg:flex lg:w-1/2 relative overflow-hidden" style="background: linear-gradient(135deg, #059669 0%, #10B981 100%);">
      <!-- GridMotion Background Pattern -->
      <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25px 25px, rgba(5, 150, 105, 0.3) 2px, transparent 0), radial-gradient(circle at 75px 75px, rgba(5, 150, 105, 0.3) 2px, transparent 0); background-size: 100px 100px;"></div>
      </div>

      <!-- Glassmorphism Overlay -->
      <div class="absolute inset-0" style="background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(16px);"></div>

      <!-- Content -->
      <div class="relative z-10 flex flex-col justify-center px-12 py-16">
        <div class="max-w-md">
          <!-- Logo with Back Button -->
          <div class="flex items-center justify-between mb-8">
            <a href="/" class="inline-block transition-all duration-300 hover:scale-105">
              <h1 class="text-4xl font-bold text-white font-['Anton',sans-serif] tracking-wide">Trodoo</h1>
            </a>
            <button
              onclick="window.location.href='/'"
              class="flex items-center space-x-2 px-4 py-2 rounded-xl transition-all duration-300 hover:scale-105"
              style="background: rgba(255, 255, 255, 0.2); backdrop-filter: blur(12px); border: 1px solid rgba(255, 255, 255, 0.3);"
              onmouseover="this.style.background='rgba(255, 255, 255, 0.3)'"
              onmouseout="this.style.background='rgba(255, 255, 255, 0.2)'"
            >
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
              </svg>
              <span class="text-white font-medium text-sm">Back</span>
            </button>
          </div>

          <!-- Tagline -->
          <h2 class="text-4xl font-bold text-white mb-6 leading-tight font-['Poppins','Helvetica_Neue',Arial,sans-serif]">
            Your Space, Your Success
          </h2>

          <p class="text-xl text-white/90 mb-8 leading-relaxed font-normal">
            Welcome back to the premier platform for discovering and booking unique venues for your special events.
          </p>

          <!-- Features -->
          <div class="space-y-4">
            <div class="flex items-center space-x-3 group">
              <div class="w-2 h-2 bg-white rounded-full group-hover:scale-125 transition-transform duration-300"></div>
              <span class="text-white/90 text-base">Trusted by thousands of event organizers</span>
            </div>
            <div class="flex items-center space-x-3 group">
              <div class="w-2 h-2 bg-white rounded-full group-hover:scale-125 transition-transform duration-300"></div>
              <span class="text-white/90 text-base">Premium venues across the country</span>
            </div>
            <div class="flex items-center space-x-3 group">
              <div class="w-2 h-2 bg-white rounded-full group-hover:scale-125 transition-transform duration-300"></div>
              <span class="text-white/90 text-base">Seamless booking experience</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Decorative GridMotion Pattern -->
      <div class="absolute bottom-0 right-0 w-64 h-64 opacity-20">
        <svg viewBox="0 0 200 200" class="w-full h-full">
          <defs>
            <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
              <path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(5, 150, 105, 0.5)" stroke-width="1"/>
            </pattern>
          </defs>
          <rect width="200" height="200" fill="url(#grid)" />
        </svg>
      </div>
    </div>

    <!-- Right Column - Form -->
    <div class="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 bg-[#F9FAFB]">
      <div class="max-w-md w-full space-y-8">
        <!-- Mobile Logo -->
        <div class="lg:hidden text-center">
          <a href="/" class="inline-block">
            <h1 class="text-3xl font-bold text-[#059669]">Trodoo</h1>
          </a>
        </div>

        <!-- Header -->
        <div class="text-center">
          <h2 class="text-heading-2 text-slate-900">
            Welcome Back
          </h2>
          <p class="mt-3 text-body text-slate-600">
            Don't have an account?
            <a href="/auth/register" class="font-medium text-primary-600 hover:text-primary-700 ml-1 transition-colors duration-300">
              Sign Up
            </a>
          </p>
        </div>

        <!-- Login Form Container -->
        <div class="card">
          <div id="login-form-container">
            <!-- React component will be mounted here -->
          </div>
        </div>

        <!-- Additional Links -->
        <div class="text-center space-y-3">
          <a href="/auth/forgot-password" class="block text-sm text-[#059669] hover:text-[#047857] transition-colors duration-300">
            Forgot your password?
          </a>
          <div class="text-sm text-[#6B7280]">
            Need help?
            <a href="/contact" class="text-[#059669] hover:text-[#047857] ml-1 transition-colors duration-300">
              Contact support
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</Layout>

<script>
  import { authActions } from '../../lib/state.js';

  // Enhanced login form with proper authentication
  document.addEventListener('DOMContentLoaded', async () => {
    const container = document.getElementById('login-form-container');
    if (!container) return;

    let isLoading = false;
    let error = '';
    let successMessage = '';

    // Check for success message from URL params (e.g., after registration)
    const urlParams = new URLSearchParams(window.location.search);
    const messageParam = urlParams.get('message');
    if (messageParam) {
      successMessage = decodeURIComponent(messageParam);
      // Remove the message from URL without refreshing
      window.history.replaceState({}, document.title, window.location.pathname);
    }

    const renderForm = () => {
      container.innerHTML = `
        <form id="login-form" class="space-y-6">
          <div>
            <label for="email" class="label">
              Email Address
            </label>
            <input
              type="email"
              id="email"
              name="email"
              required
              class="input ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}"
              placeholder="Enter your email"
              ${isLoading ? 'disabled' : ''}
            />
          </div>

          <div>
            <label for="password" class="label">
              Password
            </label>
            <input
              type="password"
              id="password"
              name="password"
              required
              class="input ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}"
              placeholder="Enter your password"
              ${isLoading ? 'disabled' : ''}
            />
          </div>

          ${successMessage ? `
            <div class="p-4 bg-green-50 border border-green-200 rounded-lg animate-fade-in">
              <p class="text-sm text-green-600">${successMessage}</p>
            </div>
          ` : ''}

          ${error ? `
            <div class="p-4 bg-red-50 border border-red-200 rounded-lg animate-fade-in">
              <p class="text-sm text-red-600">${error}</p>
            </div>
          ` : ''}

          <button
            type="submit"
            ${isLoading ? 'disabled' : ''}
            class="btn-primary w-full flex justify-center items-center"
          >
            ${isLoading ? `
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-[#1F2937]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Signing in...
            ` : 'Sign In'}
          </button>
        </form>
      `;

      // Attach event listener
      const form = document.getElementById('login-form') as HTMLFormElement;
      if (form) {
        form.addEventListener('submit', handleSubmit);
      }
    };

    const handleSubmit = async (e: Event) => {
      e.preventDefault();

      const formData = new FormData(e.target as HTMLFormElement);
      const email = formData.get('email') as string;
      const password = formData.get('password') as string;

      if (!email || !password) {
        error = 'Please fill in all fields.';
        successMessage = '';
        renderForm();
        return;
      }

      isLoading = true;
      error = '';
      successMessage = '';
      renderForm();

      try {
        const result = await authActions.login(email, password);

        if (result.success) {
          // Redirect to dashboard on success
          window.location.href = '/dashboard';
        } else {
          error = result.error || 'Login failed. Please try again.';
          successMessage = '';
          isLoading = false;
          renderForm();
        }
      } catch (err) {
        console.error('Login error:', err);
        error = 'An unexpected error occurred. Please try again.';
        successMessage = '';
        isLoading = false;
        renderForm();
      }
    };

    // Initial render
    renderForm();
  });
</script>

<style>
  /* Additional styles for the login page */
  .bg-pattern {
    background-image: 
      radial-gradient(circle at 25px 25px, rgba(5, 150, 105, 0.1) 2px, transparent 0),
      radial-gradient(circle at 75px 75px, rgba(5, 150, 105, 0.1) 2px, transparent 0);
    background-size: 100px 100px;
  }
</style>
