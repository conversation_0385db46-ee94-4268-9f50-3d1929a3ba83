---
import Layout from '../../components/core/Layout.astro';
---

<Layout 
  title="Profile Settings - Trodoo"
  description="Manage your profile settings, roles, and account preferences."
>
  <div class="min-h-screen bg-[#F9FAFB]">
    <!-- Dashboard Header -->
    <div class="bg-white shadow-sm border-b border-[#E5E7EB]">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-6">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-2xl font-bold text-[#1F2937]">Profile Settings</h1>
              <p class="mt-1 text-sm text-[#6B7280]">
                Manage your account information and preferences
              </p>
            </div>
            <div class="flex space-x-3">
              <a 
                href="/dashboard" 
                class="inline-flex items-center px-4 py-2 border-2 border-[#E5E7EB] text-sm font-medium rounded-lg text-[#6B7280] bg-white hover:bg-[#F9FAFB] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#059669] transition-all duration-300"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Dashboard
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Profile Content -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Profile Form -->
        <div class="lg:col-span-2">
          <div id="profile-form-container">
            <!-- ProfileForm component will be mounted here -->
            <div class="bg-white rounded-xl shadow-lg border border-[#E5E7EB] p-8">
              <div class="animate-pulse">
                <div class="flex items-center space-x-6 mb-6">
                  <div class="w-20 h-20 bg-[#E5E7EB] rounded-full"></div>
                  <div class="space-y-2">
                    <div class="h-6 bg-[#E5E7EB] rounded w-32"></div>
                    <div class="h-4 bg-[#E5E7EB] rounded w-48"></div>
                  </div>
                </div>
                <div class="space-y-4">
                  <div class="h-4 bg-[#E5E7EB] rounded w-24"></div>
                  <div class="h-12 bg-[#E5E7EB] rounded"></div>
                  <div class="h-4 bg-[#E5E7EB] rounded w-24"></div>
                  <div class="h-12 bg-[#E5E7EB] rounded"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
          <!-- Account Status -->
          <div class="bg-white rounded-xl shadow-lg border border-[#E5E7EB] p-6">
            <h3 class="text-lg font-bold text-[#1F2937] mb-4">Account Status</h3>
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="text-sm text-[#6B7280]">Email Verified</span>
                <span class="px-2 py-1 bg-[#D1FAE5] text-[#059669] text-xs rounded-full font-medium">
                  Verified
                </span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-[#6B7280]">Account Status</span>
                <span class="px-2 py-1 bg-[#D1FAE5] text-[#059669] text-xs rounded-full font-medium">
                  Active
                </span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-[#6B7280]">Member Since</span>
                <span class="text-sm text-[#1F2937]" id="member-since">
                  Loading...
                </span>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="bg-white rounded-xl shadow-lg border border-[#E5E7EB] p-6">
            <h3 class="text-lg font-bold text-[#1F2937] mb-4">Quick Actions</h3>
            <div class="space-y-3">
              <a href="/venues/new" class="block p-3 border border-[#E5E7EB] rounded-lg hover:bg-[#F9FAFB] transition-colors duration-300">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-[#059669]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h4 class="text-sm font-medium text-[#1F2937]">List a Venue</h4>
                    <p class="text-xs text-[#6B7280]">Add your space to the marketplace</p>
                  </div>
                </div>
              </a>

              <a href="/venues" class="block p-3 border border-[#E5E7EB] rounded-lg hover:bg-[#F9FAFB] transition-colors duration-300">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-[#059669]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h4 class="text-sm font-medium text-[#1F2937]">Browse Venues</h4>
                    <p class="text-xs text-[#6B7280]">Find the perfect space</p>
                  </div>
                </div>
              </a>

              <a href="/bookings" class="block p-3 border border-[#E5E7EB] rounded-lg hover:bg-[#F9FAFB] transition-colors duration-300">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-[#059669]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h4 class="text-sm font-medium text-[#1F2937]">My Bookings</h4>
                    <p class="text-xs text-[#6B7280]">View your reservations</p>
                  </div>
                </div>
              </a>
            </div>
          </div>

          <!-- Support -->
          <div class="bg-white rounded-xl shadow-lg border border-[#E5E7EB] p-6">
            <h3 class="text-lg font-bold text-[#1F2937] mb-4">Need Help?</h3>
            <p class="text-sm text-[#6B7280] mb-4">
              Our support team is here to help you with any questions or issues.
            </p>
            <a 
              href="/contact" 
              class="inline-flex items-center px-4 py-2 bg-[#F59E0B] text-[#1F2937] text-sm font-medium rounded-lg hover:bg-[#D97706] transition-colors duration-300"
            >
              Contact Support
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</Layout>

<script>
  import { userStore, authLoadingStore, initializeAuth } from '../../lib/state.ts';

  document.addEventListener('DOMContentLoaded', async () => {
    // Initialize authentication state
    initializeAuth();

    // Wait for auth to load
    const checkAuth = () => {
      const user = userStore.get();
      const isLoading = authLoadingStore.get();

      console.log('Profile page - Auth check:', { user: !!user, isLoading });

      if (isLoading) {
        // Still loading, wait
        return;
      }

      if (user) {
        loadProfileForm();
        updateMemberSince(user.created);
      } else {
        // Only redirect if auth loading is complete and no user found
        console.log('Profile page - No user found after auth loading complete, redirecting to login');
        window.location.href = '/auth/login';
      }
    };

    // Check auth state initially
    checkAuth();

    // Listen for auth changes
    userStore.subscribe(checkAuth);

    // Also listen for auth loading changes
    authLoadingStore.subscribe(checkAuth);

    function loadProfileForm() {
      const container = document.getElementById('profile-form-container');
      if (container) {
        // This would normally load the React component
        // For now, we'll show a placeholder
        container.innerHTML = `
          <div class="bg-white rounded-xl shadow-lg border border-[#E5E7EB] p-8">
            <h3 class="text-xl font-bold text-[#1F2937] mb-6">Profile Information</h3>
            <p class="text-[#6B7280]">
              Profile form component will be integrated here with React.
              This includes avatar upload, name editing, and role management.
            </p>
          </div>
        `;
      }
    }

    function updateMemberSince(createdDate: string) {
      const memberSinceEl = document.getElementById('member-since');
      if (memberSinceEl && createdDate) {
        const date = new Date(createdDate);
        const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short' };
        memberSinceEl.textContent = date.toLocaleDateString('en-US', options);
      }
    }
  });
</script>

<style>
  /* Additional styles for the profile page */
  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: .5;
    }
  }
</style>
