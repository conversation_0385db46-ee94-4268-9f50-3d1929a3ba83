---
import BaseLayout from '../../layouts/BaseLayout.astro';

// Check if user is authenticated
const authCookie = Astro.cookies.get('pb_auth');
if (!authCookie) {
  return Astro.redirect('/auth/login');
}
---

<BaseLayout
  title="Dashboard - Trodoo"
  description="Manage your bookings, venues, and account settings."
>
  <main class="min-h-screen bg-gradient-to-br from-slate-50 to-white">
    <!-- Dashboard Header -->
    <div class="bg-white border-b border-slate-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-8">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-3xl font-bold text-slate-900">Dashboard</h1>
              <p class="mt-2 text-slate-600">
                Welcome back! Here's what's happening with your account.
              </p>
            </div>
            <div class="flex items-center space-x-3">
              <a
                href="/venues/new"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-green hover:bg-primary-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-green transition-colors"
              >
                List Your Venue
              </a>
              <a
                href="/venues"
                class="inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-green transition-colors"
              >
                Browse Venues
              </a>

              <!-- Profile Dropdown -->
              <div class="relative">
                <button
                  id="profile-menu-button"
                  class="flex items-center justify-center w-10 h-10 rounded-full bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-300 hover:scale-105"
                  aria-expanded="false"
                  aria-haspopup="true"
                >
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                </button>

                <!-- Dropdown Menu -->
                <div
                  id="profile-menu"
                  class="hidden absolute right-0 mt-2 w-56 bg-white rounded-xl shadow-lg border border-slate-200 py-2 z-50 transform opacity-0 scale-95 transition-all duration-200"
                  style="box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);"
                >
                  <!-- User Info Section -->
                  <div class="px-4 py-3 border-b border-slate-200">
                    <p class="text-sm font-medium text-slate-900" id="user-name">Loading...</p>
                    <p class="text-sm text-slate-600" id="user-email">Loading...</p>
                  </div>

                  <!-- Menu Items -->
                  <div class="py-1">
                    <a
                      href="/dashboard/profile"
                      class="flex items-center px-4 py-2 text-sm text-slate-700 hover:bg-slate-50 hover:text-primary-600 transition-colors duration-200"
                    >
                      <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                      </svg>
                      Profile Settings
                    </a>
                    <a
                      href="/bookings"
                      class="flex items-center px-4 py-2 text-sm text-slate-700 hover:bg-slate-50 hover:text-primary-600 transition-colors duration-200"
                    >
                      <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                      </svg>
                      My Bookings
                    </a>
                    <a
                      href="/venues/my-venues"
                      class="flex items-center px-4 py-2 text-sm text-slate-700 hover:bg-slate-50 hover:text-primary-600 transition-colors duration-200"
                    >
                      <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                      </svg>
                      My Venues
                    </a>
                  </div>

                  <!-- Logout Section -->
                  <div class="border-t border-slate-200 py-1">
                    <button
                      id="logout-button"
                      class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200"
                    >
                      <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                      </svg>
                      Sign Out
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Dashboard Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Welcome Message (for new users) -->
      <div id="welcome-banner" class="hidden mb-8 bg-primary-50 border border-primary-200 rounded-lg p-6">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-primary-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-primary-800">
              Welcome to Trodoo!
            </h3>
            <div class="mt-2 text-sm text-primary-700">
              <p>Your account has been created successfully. Start by browsing venues or listing your own space.</p>
            </div>
            <div class="mt-4">
              <div class="-mx-2 -my-1.5 flex">
                <button type="button" class="bg-primary-50 px-2 py-1.5 rounded-md text-sm font-medium text-primary-800 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-primary-50 focus:ring-primary-600" onclick="this.parentElement.parentElement.parentElement.parentElement.style.display='none'">
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Stats Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    Active Bookings
                  </dt>
                  <dd class="text-lg font-medium text-gray-900" id="active-bookings-count">
                    Loading...
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    Listed Venues
                  </dt>
                  <dd class="text-lg font-medium text-gray-900" id="venues-count">
                    Loading...
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    Total Earnings
                  </dt>
                  <dd class="text-lg font-medium text-gray-900" id="earnings-total">
                    Loading...
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Recent Bookings -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Bookings</h3>
          </div>
          <div class="p-6">
            <div id="recent-bookings-container">
              <!-- BookingList component will be mounted here -->
              <div class="text-center py-8 text-gray-500">
                Loading bookings...
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
          </div>
          <div class="p-6">
            <div class="space-y-4">
              <a href="/venues/new" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </div>
                  <div class="ml-4">
                    <h4 class="text-sm font-medium text-gray-900">List a New Venue</h4>
                    <p class="text-sm text-gray-500">Add your space to the marketplace</p>
                  </div>
                </div>
              </a>

              <a href="/venues" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <div class="ml-4">
                    <h4 class="text-sm font-medium text-gray-900">Browse Venues</h4>
                    <p class="text-sm text-gray-500">Find the perfect space for your event</p>
                  </div>
                </div>
              </a>

              <a href="/dashboard/profile" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <div class="ml-4">
                    <h4 class="text-sm font-medium text-gray-900">Update Profile</h4>
                    <p class="text-sm text-gray-500">Manage your account settings</p>
                  </div>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
</BaseLayout>

<!-- React Components Integration -->
<script>
  import BookingList from '../../components/dashboard/BookingList.tsx';
  import { createRoot } from 'react-dom/client';
  import React from 'react';
  import { getUserBookings } from '../../lib/pocketbase.ts';
  import { authActions, userStore, initializeAuth, authLoadingStore } from '../../lib/state.js';

  // Helper function to wait for authentication to complete
  function waitForAuth(): Promise<void> {
    return new Promise((resolve) => {
      const checkAuth = () => {
        if (!authLoadingStore.get()) {
          resolve();
        } else {
          setTimeout(checkAuth, 100);
        }
      };
      checkAuth();
    });
  }

  // Dashboard initialization
  document.addEventListener('DOMContentLoaded', async () => {
    // Initialize authentication state first
    initializeAuth();

    // Wait for auth to be ready
    await waitForAuth();

    // Initialize profile dropdown functionality
    initializeProfileDropdown();

    // Load user information
    loadUserInfo();

    // Check for welcome parameter
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('welcome') === 'true') {
      const welcomeBanner = document.getElementById('welcome-banner');
      if (welcomeBanner) {
        welcomeBanner.classList.remove('hidden');
      }
    }

    // Get current user from state
    const user = userStore.get();
    if (!user) {
      console.error('No authenticated user found');
      window.location.href = '/auth/login';
      return;
    }

    const currentUserId = user.id;

    // Profile dropdown functionality
    function initializeProfileDropdown() {
      const profileButton = document.getElementById('profile-menu-button');
      const profileMenu = document.getElementById('profile-menu');
      const logoutButton = document.getElementById('logout-button');

      console.log('Initializing profile dropdown:', { profileButton, profileMenu, logoutButton });

      if (!profileButton || !profileMenu || !logoutButton) {
        console.error('Profile dropdown elements not found');
        return;
      }

      // Test if button is clickable
      console.log('Profile button styles:', window.getComputedStyle(profileButton));
      console.log('Profile menu initial classes:', profileMenu.className);

      // Toggle dropdown on button click
      profileButton.addEventListener('click', (e) => {
        e.stopPropagation();
        const isHidden = profileMenu.classList.contains('hidden');

        console.log('Profile button clicked, dropdown is hidden:', isHidden);

        if (isHidden) {
          // Show menu
          console.log('Showing dropdown menu');
          profileMenu.classList.remove('hidden');
          setTimeout(() => {
            profileMenu.classList.remove('opacity-0', 'scale-95');
            profileMenu.classList.add('opacity-100', 'scale-100');
          }, 10);
          profileButton.setAttribute('aria-expanded', 'true');
        } else {
          // Hide menu
          console.log('Hiding dropdown menu');
          profileMenu.classList.remove('opacity-100', 'scale-100');
          profileMenu.classList.add('opacity-0', 'scale-95');
          setTimeout(() => {
            profileMenu.classList.add('hidden');
          }, 200);
          profileButton.setAttribute('aria-expanded', 'false');
        }
      });

      // Close dropdown when clicking outside
      document.addEventListener('click', (e) => {
        const target = e.target as HTMLElement;
        if (target && !profileButton.contains(target) && !profileMenu.contains(target)) {
          profileMenu.classList.remove('opacity-100', 'scale-100');
          profileMenu.classList.add('opacity-0', 'scale-95');
          setTimeout(() => {
            profileMenu.classList.add('hidden');
          }, 200);
          profileButton.setAttribute('aria-expanded', 'false');
        }
      });

      // Close dropdown on escape key
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && !profileMenu.classList.contains('hidden')) {
          profileMenu.classList.remove('opacity-100', 'scale-100');
          profileMenu.classList.add('opacity-0', 'scale-95');
          setTimeout(() => {
            profileMenu.classList.add('hidden');
          }, 200);
          profileButton.setAttribute('aria-expanded', 'false');
        }
      });

      // Logout functionality
      logoutButton.addEventListener('click', async (e) => {
        e.preventDefault();

        // Add loading state
        logoutButton.innerHTML = `
          <svg class="w-4 h-4 mr-3 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Signing out...
        `;
        (logoutButton as HTMLButtonElement).disabled = true;

        try {
          // Call logout action
          authActions.logout();
        } catch (error) {
          console.error('Logout error:', error);
          // Force redirect if logout fails
          window.location.href = '/';
        }
      });
    }

    // Load user information for profile dropdown
    function loadUserInfo() {
      try {
        const user = userStore.get();
        if (user) {
          updateUserInfo(user);
        } else {
          // Listen for user store changes
          userStore.subscribe((user) => {
            if (user) {
              updateUserInfo(user);
            }
          });
        }
      } catch (error) {
        console.error('Failed to load user info:', error);
      }
    }

    function updateUserInfo(user: any) {
      const userNameEl = document.getElementById('user-name');
      const userEmailEl = document.getElementById('user-email');

      if (userNameEl && user.name) {
        userNameEl.textContent = user.name;
      }

      if (userEmailEl && user.email) {
        userEmailEl.textContent = user.email;
      }
    }

    // Load dashboard data
    async function loadDashboardData() {
      try {
        // Load bookings for both renter and owner roles
        const [renterBookings, ownerBookings] = await Promise.all([
          getUserBookings(currentUserId || undefined, 1, 10, 'renter'),
          getUserBookings(currentUserId || undefined, 1, 10, 'owner')
        ]);

        const allBookings = [
          ...(renterBookings.success ? renterBookings.bookings : []),
          ...(ownerBookings.success ? ownerBookings.bookings : [])
        ];

        // Update stats
        const activeBookings = allBookings.filter((b: any) => ['confirmed', 'paid'].includes(b.status));
        const totalEarnings = ownerBookings.success
          ? ownerBookings.bookings
              .filter((b: any) => b.status === 'completed')
              .reduce((sum: number, b: any) => sum + (b.payout_amount || 0), 0)
          : 0;

        // Update stat cards
        const activeBookingsEl = document.getElementById('active-bookings-count');
        if (activeBookingsEl) activeBookingsEl.textContent = activeBookings.length.toString();

        const venuesCountEl = document.getElementById('venues-count');
        if (venuesCountEl) venuesCountEl.textContent = '0'; // TODO: Load actual venue count

        const earningsEl = document.getElementById('earnings-total');
        if (earningsEl) {
          earningsEl.textContent = new Intl.NumberFormat('en-NG', {
            style: 'currency',
            currency: 'NGN',
            minimumFractionDigits: 0
          }).format(totalEarnings);
        }

        // Mount BookingList component
        const container = document.getElementById('recent-bookings-container');
        if (container && allBookings.length > 0) {
          const root = createRoot(container);

          const handleBookingAction = (bookingId: string, action: string) => {
            switch (action) {
              case 'view':
              case 'message':
                window.location.href = `/bookings/${bookingId}`;
                break;
              case 'pay':
                window.location.href = `/bookings/${bookingId}?action=pay`;
                break;
              default:
                window.location.href = `/bookings/${bookingId}`;
            }
          };

          // Determine primary user role based on bookings
          const primaryRole = ownerBookings.success && ownerBookings.bookings.length > 0 ? 'owner' : 'renter';

          root.render(
            React.createElement(BookingList, {
              bookings: allBookings.slice(0, 5), // Show only recent 5
              userRole: primaryRole,
              onBookingAction: handleBookingAction,
              isLoading: false
            })
          );
        } else if (container) {
          // Show empty state
          container.innerHTML = `
            <div class="text-center py-8">
              <div class="text-slate-400 text-lg mb-2">No bookings yet</div>
              <p class="text-slate-600 mb-4">Start by browsing venues or listing your own space.</p>
              <div class="space-x-3">
                <a href="/venues" class="inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-50 transition-colors">
                  Browse Venues
                </a>
                <a href="/venues/new" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 transition-colors">
                  List Your Venue
                </a>
              </div>
            </div>
          `;
        }

      } catch (error) {
        console.error('Failed to load dashboard data:', error);

        // Show error state
        const container = document.getElementById('recent-bookings-container');
        if (container) {
          container.innerHTML = `
            <div class="text-center py-8">
              <div class="text-red-500 text-lg mb-2">Failed to load bookings</div>
              <p class="text-slate-600 mb-4">There was an error loading your dashboard data.</p>
              <button onclick="window.location.reload()" class="inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-50 transition-colors">
                Try Again
              </button>
            </div>
          `;
        }
      }
    }

    // Initialize dashboard
    await loadDashboardData();
  });
</script>

<style>
  /* Profile dropdown animations */
  #profile-menu {
    transform-origin: top right;
  }

  /* Smooth transitions for dropdown */
  .transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Hover effects for profile button */
  #profile-menu-button:hover {
    transform: scale(1.05);
  }

  /* Focus styles for accessibility */
  #profile-menu-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(5, 150, 105, 0.5);
  }

  /* Menu item hover effects */
  #profile-menu a:hover,
  #profile-menu button:hover {
    transform: translateX(2px);
  }
</style>
