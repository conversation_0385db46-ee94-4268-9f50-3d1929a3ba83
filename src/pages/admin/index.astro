---
import AdminLayout from '../../layouts/AdminLayout.astro';
---

<AdminLayout title="Admin Dashboard - Trodoo">
  <div class="space-y-8">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-[#1F2937]">Admin Dashboard</h1>
        <p class="mt-2 text-[#6B7280]">
          Monitor platform activity and manage system operations
        </p>
      </div>
      
      <div class="flex space-x-4">
        <a 
          href="/admin/users" 
          class="inline-flex items-center px-4 py-2 bg-[#F59E0B] text-[#1F2937] text-sm font-medium rounded-lg hover:bg-[#D97706] transition-colors duration-300"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
          </svg>
          Manage Users
        </a>
        
        <a 
          href="/admin/flagged-content" 
          class="inline-flex items-center px-4 py-2 border-2 border-[#059669] text-[#059669] text-sm font-medium rounded-lg hover:bg-[#059669] hover:text-white transition-all duration-300"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
          Review Flags
        </a>
      </div>
    </div>

    <!-- Dashboard Content -->
    <div id="admin-dashboard-container">
      <!-- Loading State -->
      <div class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div class="bg-white rounded-xl shadow-lg border border-[#E5E7EB] p-6 animate-pulse">
            <div class="h-4 bg-[#E5E7EB] rounded w-24 mb-4"></div>
            <div class="h-8 bg-[#E5E7EB] rounded w-16"></div>
          </div>
          <div class="bg-white rounded-xl shadow-lg border border-[#E5E7EB] p-6 animate-pulse">
            <div class="h-4 bg-[#E5E7EB] rounded w-24 mb-4"></div>
            <div class="h-8 bg-[#E5E7EB] rounded w-16"></div>
          </div>
          <div class="bg-white rounded-xl shadow-lg border border-[#E5E7EB] p-6 animate-pulse">
            <div class="h-4 bg-[#E5E7EB] rounded w-24 mb-4"></div>
            <div class="h-8 bg-[#E5E7EB] rounded w-16"></div>
          </div>
          <div class="bg-white rounded-xl shadow-lg border border-[#E5E7EB] p-6 animate-pulse">
            <div class="h-4 bg-[#E5E7EB] rounded w-24 mb-4"></div>
            <div class="h-8 bg-[#E5E7EB] rounded w-16"></div>
          </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-lg border border-[#E5E7EB] p-6 animate-pulse">
          <div class="h-6 bg-[#E5E7EB] rounded w-48 mb-6"></div>
          <div class="space-y-4">
            <div class="h-16 bg-[#E5E7EB] rounded"></div>
            <div class="h-16 bg-[#E5E7EB] rounded"></div>
            <div class="h-16 bg-[#E5E7EB] rounded"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</AdminLayout>

<script>
  // This would normally import and render the React AdminDashboard component
  // For now, we'll show a placeholder with some interactive elements
  
  document.addEventListener('DOMContentLoaded', () => {
    const container = document.getElementById('admin-dashboard-container');
    if (!container) return;

    // Simulate loading time
    setTimeout(() => {
      container.innerHTML = `
        <div class="space-y-8">
          <!-- Stats Grid -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="bg-white rounded-xl shadow-lg border border-[#E5E7EB] p-6">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-[#6B7280]">Total Users</p>
                  <p class="text-2xl font-bold text-[#1F2937]">1,247</p>
                </div>
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                  </svg>
                </div>
              </div>
              <div class="mt-4">
                <span class="text-sm text-green-600 font-medium">892 active</span>
              </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg border border-[#E5E7EB] p-6">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-[#6B7280]">Total Venues</p>
                  <p class="text-2xl font-bold text-[#1F2937]">156</p>
                </div>
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                  </svg>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg border border-[#E5E7EB] p-6">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-[#6B7280]">Pending Flags</p>
                  <p class="text-2xl font-bold text-[#1F2937]">3</p>
                </div>
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                  <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                  </svg>
                </div>
              </div>
              <div class="mt-4">
                <a href="/admin/flagged-content" class="text-sm text-red-600 hover:text-red-700 font-medium transition-colors duration-300">
                  Review now →
                </a>
              </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg border border-[#E5E7EB] p-6">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-[#6B7280]">Total Bookings</p>
                  <p class="text-2xl font-bold text-[#1F2937]">2,341</p>
                </div>
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                </div>
              </div>
            </div>
          </div>

          <!-- Recent Activity -->
          <div class="bg-white rounded-xl shadow-lg border border-[#E5E7EB] p-6">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-lg font-bold text-[#1F2937]">Recent Activity</h3>
              <button class="text-sm text-[#059669] hover:text-[#047857] font-medium transition-colors duration-300">
                Refresh
              </button>
            </div>
            
            <div class="space-y-4">
              <div class="flex items-start space-x-4 p-4 bg-[#F9FAFB] rounded-lg">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-[#1F2937]">New user registration</p>
                  <p class="text-xs text-[#6B7280] mt-1"><EMAIL></p>
                </div>
                <span class="text-xs text-[#6B7280] whitespace-nowrap">2h ago</span>
              </div>

              <div class="flex items-start space-x-4 p-4 bg-[#F9FAFB] rounded-lg">
                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                  </svg>
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-[#1F2937]">New venue listed: "Modern Conference Center"</p>
                  <p class="text-xs text-[#6B7280] mt-1"><EMAIL></p>
                </div>
                <span class="text-xs text-[#6B7280] whitespace-nowrap">4h ago</span>
              </div>

              <div class="flex items-start space-x-4 p-4 bg-[#F9FAFB] rounded-lg">
                <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                  <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                  </svg>
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-[#1F2937]">Content flagged for review</p>
                  <p class="text-xs text-[#6B7280] mt-1"><EMAIL></p>
                </div>
                <span class="text-xs text-[#6B7280] whitespace-nowrap">6h ago</span>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-white rounded-xl shadow-lg border border-[#E5E7EB] p-6">
              <h3 class="text-lg font-bold text-[#1F2937] mb-4">Quick Actions</h3>
              <div class="space-y-3">
                <a href="/admin/users" class="block p-3 border border-[#E5E7EB] rounded-lg hover:bg-[#F9FAFB] transition-colors duration-300">
                  <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-[#1F2937]">Manage Users</span>
                    <svg class="w-4 h-4 text-[#6B7280]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                  </div>
                </a>
                <a href="/admin/flagged-content" class="block p-3 border border-[#E5E7EB] rounded-lg hover:bg-[#F9FAFB] transition-colors duration-300">
                  <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-[#1F2937]">Review Flagged Content</span>
                    <svg class="w-4 h-4 text-[#6B7280]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                  </div>
                </a>
              </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg border border-[#E5E7EB] p-6">
              <h3 class="text-lg font-bold text-[#1F2937] mb-4">System Status</h3>
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <span class="text-sm text-[#6B7280]">Database</span>
                  <span class="px-2 py-1 bg-green-100 text-green-600 text-xs rounded-full font-medium">
                    Healthy
                  </span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-[#6B7280]">Search Service</span>
                  <span class="px-2 py-1 bg-green-100 text-green-600 text-xs rounded-full font-medium">
                    Operational
                  </span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-[#6B7280]">Payment Gateway</span>
                  <span class="px-2 py-1 bg-green-100 text-green-600 text-xs rounded-full font-medium">
                    Connected
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      `;
    }, 1500);
  });
</script>

<style>
  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: .5;
    }
  }
</style>
