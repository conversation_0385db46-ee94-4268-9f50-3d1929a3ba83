---
import AdminLayout from '../../layouts/AdminLayout.astro';
---

<AdminLayout title="Flagged Content - Admin Dashboard">
  <div class="space-y-8">
    <!-- Page Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-[#1F2937]">Flagged Content</h1>
        <p class="mt-2 text-[#6B7280]">
          Review and manage content that has been reported by users
        </p>
      </div>
      
      <div class="flex space-x-4">
        <a 
          href="/admin" 
          class="inline-flex items-center px-4 py-2 border-2 border-[#E5E7EB] text-sm font-medium rounded-lg text-[#6B7280] bg-white hover:bg-[#F9FAFB] transition-all duration-300"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          Back to Dashboard
        </a>
        
        <a 
          href="/admin/users" 
          class="inline-flex items-center px-4 py-2 bg-[#F59E0B] text-[#1F2937] text-sm font-medium rounded-lg hover:bg-[#D97706] transition-colors duration-300"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
          </svg>
          Manage Users
        </a>
      </div>
    </div>

    <!-- Flagged Content Table -->
    <div id="flagged-content-container">
      <!-- Loading State -->
      <div class="bg-white rounded-xl shadow-lg border border-[#E5E7EB] p-6">
        <div class="animate-pulse space-y-4">
          <div class="flex justify-between items-center">
            <div class="h-6 bg-[#E5E7EB] rounded w-48"></div>
            <div class="flex space-x-4">
              <div class="h-10 bg-[#E5E7EB] rounded w-64"></div>
              <div class="h-10 bg-[#E5E7EB] rounded w-32"></div>
            </div>
          </div>
          <div class="space-y-3">
            <div class="h-16 bg-[#E5E7EB] rounded"></div>
            <div class="h-16 bg-[#E5E7EB] rounded"></div>
            <div class="h-16 bg-[#E5E7EB] rounded"></div>
            <div class="h-16 bg-[#E5E7EB] rounded"></div>
            <div class="h-16 bg-[#E5E7EB] rounded"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</AdminLayout>

<script>
  // This would normally import and render the React FlaggedContentTable component
  // For now, we'll show a placeholder with the table structure
  
  document.addEventListener('DOMContentLoaded', () => {
    const container = document.getElementById('flagged-content-container');
    if (!container) return;

    // Simulate loading time
    setTimeout(() => {
      container.innerHTML = `
        <div class="bg-white rounded-xl shadow-lg border border-[#E5E7EB]">
          <!-- Header -->
          <div class="p-6 border-b border-[#E5E7EB]">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
              <div>
                <h3 class="text-lg font-bold text-[#1F2937]">Flagged Content</h3>
                <p class="text-sm text-[#6B7280]">
                  Review and manage reported content
                </p>
              </div>
              
              <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
                <!-- Search -->
                <div class="relative">
                  <input
                    type="text"
                    placeholder="Search flags..."
                    class="w-full sm:w-64 px-4 py-2 pl-10 border border-[#E5E7EB] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#059669] focus:border-[#059669]"
                  />
                  <svg class="absolute left-3 top-2.5 h-4 w-4 text-[#6B7280]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                  </svg>
                </div>
                
                <!-- Filter -->
                <select class="px-4 py-2 border border-[#E5E7EB] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#059669] focus:border-[#059669]">
                  <option value="all">All Status</option>
                  <option value="open">Open</option>
                  <option value="resolved">Resolved</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Table -->
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-[#F9FAFB] border-b border-[#E5E7EB]">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-[#6B7280] uppercase tracking-wider">
                    Content
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-[#6B7280] uppercase tracking-wider">
                    Reporter
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-[#6B7280] uppercase tracking-wider">
                    Reason
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-[#6B7280] uppercase tracking-wider">
                    Status
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-[#6B7280] uppercase tracking-wider">
                    Date
                  </th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-[#6B7280] uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-[#E5E7EB]">
                <tr class="hover:bg-[#F9FAFB] transition-colors duration-200">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center space-x-3">
                      <span class="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded-full font-medium">
                        Venue
                      </span>
                      <button class="text-sm text-[#059669] hover:text-[#047857] font-medium transition-colors duration-300">
                        venue_123
                      </button>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-[#1F2937]">John Doe</div>
                      <div class="text-sm text-[#6B7280]"><EMAIL></div>
                    </div>
                  </td>
                  <td class="px-6 py-4">
                    <div class="text-sm text-[#1F2937] max-w-xs truncate" title="Inappropriate images in venue gallery">
                      Inappropriate images in venue gallery
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 py-1 bg-red-100 text-red-600 text-xs rounded-full font-medium">
                      Open
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-[#6B7280]">
                    Jan 15, 2024, 10:30 AM
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex items-center justify-end space-x-2">
                      <button class="text-[#059669] hover:text-[#047857] transition-colors duration-300" title="View Content">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                      </button>
                      <button class="text-green-600 hover:text-green-700 transition-colors duration-300" title="Resolve Flag">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>

                <tr class="hover:bg-[#F9FAFB] transition-colors duration-200">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center space-x-3">
                      <span class="px-2 py-1 bg-purple-100 text-purple-600 text-xs rounded-full font-medium">
                        Profile
                      </span>
                      <button class="text-sm text-[#059669] hover:text-[#047857] font-medium transition-colors duration-300">
                        user_456
                      </button>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-[#1F2937]">Jane Smith</div>
                      <div class="text-sm text-[#6B7280]"><EMAIL></div>
                    </div>
                  </td>
                  <td class="px-6 py-4">
                    <div class="text-sm text-[#1F2937] max-w-xs truncate" title="Fake profile with stolen photos">
                      Fake profile with stolen photos
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 py-1 bg-red-100 text-red-600 text-xs rounded-full font-medium">
                      Open
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-[#6B7280]">
                    Jan 14, 2024, 3:20 PM
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex items-center justify-end space-x-2">
                      <button class="text-[#059669] hover:text-[#047857] transition-colors duration-300" title="View Content">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                      </button>
                      <button class="text-green-600 hover:text-green-700 transition-colors duration-300" title="Resolve Flag">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>

                <tr class="hover:bg-[#F9FAFB] transition-colors duration-200">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center space-x-3">
                      <span class="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded-full font-medium">
                        Venue
                      </span>
                      <button class="text-sm text-[#059669] hover:text-[#047857] font-medium transition-colors duration-300">
                        venue_789
                      </button>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-[#1F2937]">Mike Johnson</div>
                      <div class="text-sm text-[#6B7280]"><EMAIL></div>
                    </div>
                  </td>
                  <td class="px-6 py-4">
                    <div class="text-sm text-[#1F2937] max-w-xs truncate" title="Misleading venue description and pricing">
                      Misleading venue description and pricing
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 py-1 bg-green-100 text-green-600 text-xs rounded-full font-medium">
                      Resolved
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-[#6B7280]">
                    Jan 13, 2024, 9:15 AM
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex items-center justify-end space-x-2">
                      <button class="text-[#059669] hover:text-[#047857] transition-colors duration-300" title="View Content">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Footer -->
          <div class="px-6 py-4 border-t border-[#E5E7EB] bg-[#F9FAFB]">
            <div class="flex items-center justify-between">
              <p class="text-sm text-[#6B7280]">
                Showing 3 of 3 flags
              </p>
              <button class="text-sm text-[#059669] hover:text-[#047857] font-medium transition-colors duration-300">
                Refresh
              </button>
            </div>
          </div>
        </div>
      `;
    }, 1500);
  });
</script>

<style>
  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: .5;
    }
  }
</style>
